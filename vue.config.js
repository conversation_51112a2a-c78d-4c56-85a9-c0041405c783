/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-20 22:11:58
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2022-09-24 18:30:39
 * @FilePath: /funds_approve_mobile/vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const path = require('path');

const resolve = (dir) => path.join(__dirname, dir);
module.exports = {
	chainWebpack: (config) => {
		config.resolve.alias // 添加别名
			.set('@', resolve('src'))
			.set('@pages', resolve('src/pages'))
			.set('@ePages', resolve('src/pages/estate'))
			.set('@bPages', resolve('src/pages/bureau'))
			.set('@assets', resolve('src/assets'))
			.set('@imgs', resolve('src/assets/img'))
			.set('@eImgs', resolve('src/assets/img/estate'))
			.set('@bImgs', resolve('src/assets/img/bureau'));
	},
	transpileDependencies: ['uview-ui'],
};
