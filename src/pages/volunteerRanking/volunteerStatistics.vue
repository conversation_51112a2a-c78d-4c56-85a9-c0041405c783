<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="志愿统计"
				title=" "
				:bgColor="bgColor"
				@leftClick="leftClick"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="main-top-image">
				<u-swiper
					:list="list"
					keyName="advertPic"
					@change="e => (current = e.current)"
					@click="previewImage"
					:autoplay="false"
					height="282rpx"
					round="30rpx"></u-swiper>
			</view>

			<view class="table-box">
				<view class="table">
					<view class="tr">
						<view class="tb tb-r">
							<text>排名</text>
						</view>
						<view
							class="tb"
							style="width: 130rpx">
							<text>昵称</text>
						</view>
						<view
							class="tb"
							style="width: 140rpx">
							<text>学校</text>
						</view>
						<view
							class="tb"
							style="width: 140rpx">
							<text>学院</text>
						</view>
						<view
							class="tb"
							style="width: 208rpx">
							<text>专业</text>
						</view>
					</view>
					<view
						class="tbody"
						v-for="(item, index) in wishList"
						:key="index">
						<view class="tbody-tr">
							<view
								class="tbody-tb"
								style="border-left: 1rpx solid #ebecf0">
								<text>{{ index + 1 }}</text>
							</view>
							<view
								class="tbody-tb"
								style="width: 130rpx">
								<text
									class="clamp"
									style="width: 110rpx; word-wrap: break-word; overflow-wrap: break-word">
									{{ item.nickName }}
								</text>
							</view>

							<view
								class="tbody-tb"
								style="width: 140rpx">
								<text
									class="clamp"
									style="width: 100rpx"
									>{{ item.school }}</text
								>
							</view>
							<view
								class="tbody-tb"
								style="width: 140rpx">
								<text
									class="clamp"
									style="width: 127rpx"
									>{{ item.schoolDepartment }}</text
								>
							</view>
							<view
								class="tbody-tb"
								style="width: 198rpx; justify-content: flex-start; padding-left: 10rpx">
								<text class="clamp">{{ item.schoolProfession }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getAdList, getWishList, getUserWish, requestOpenId } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			//图片基准地址
			imagebaseurl: imagebaseurl,
			list: [],
			user: uni.getStorageSync("user"),
			//公告列表参数
			params: {
				pageNo: 1,
				pageSize: 10,
				keyWord: "",
			},
			wishList: [],
			openId: "",
			userWish: {},
			current: 0,
		};
	},
	created() {
		let openId = this.$store.state.openId || uni.getStorageSync("openId");
		this.openId = openId;
		let self = this;
		if (this.openId) {
			this.initData();
		} else {
			uni.login({
				provider: "weixin",
				success: function (loginRes) {
					self.requestOpenIdResp(loginRes.code);
				},
			});
		}
	},
	onLoad() {
		wx.showShareMenu({
			title: "立格致远教育",
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
		});
	},

	mounted() {
		this.getElementHeight();
	},
	onShareAppMessage() {
		return {
			title: `立格致远教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},
	methods: {
		//图片预览
		previewImage() {
      let currAd = this.list[this.current];
      if (currAd.jumpParam) {
        if (currAd.jumpType == 1) {
          uni.navigateTo({
            url: currAd.jumpParam,
          });

          return;
        }else if (currAd.jumpType == 3) {
          wx.navigateToMiniProgram({
            appId: currAd.jumpParam,
            fail(err) {
              // 打开失败
              console.log(">>打开商城失败>>err>>", err);
              uni.showModal({
                title: "提示",
                content: "跳转失败，请在微信小程序中搜索该小程序",
                showCancel: false,
                success: function (res) {
                }
              });
            },
            success(res) {
              // 打开成功
            }
          })
          
          return;
        }
      }
      
			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});

			uni.previewImage({
				current: this.current,
				urls: urls, // 需要预览的图片http链接列表
				loop: true,
			});
		},
		// 获取openid
		requestOpenIdResp(code) {
			requestOpenId({
				code: code,
			})
				.then(res => {
					console.log("res: " + JSON.stringify(res));
					if (res.result == "1") {
						this.openId = res.data.openid;
						uni.removeStorageSync("openId");
						uni.setStorageSync("openId", this.openId);
						this.sessionKey = res.data.sessionKey;
						this.$store.commit("updateOpenId", this.openId);
						this.initData();
					} else if (res.result == "200123") {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err || "授权失败，请重试");
				});
		},
		initData() {
			// this.getWishListInfo();
			this.getUserWish();
		},
		leftClick() {
			let startPage = uni.getStorageSync("startPage");
			if (uni.getStorageSync("startPage") && startPage != "") {
				uni.switchTab({
					url: uni.getStorageSync("startPage"),
				});
			} else {
				uni.reLaunch({
					url: "/pages/login/phoneLogin",
				});
			}
		},
		//获取用户填报记录
		getUserWish() {
			getUserWish({ openid: this.openId })
				.then(res => {
					if (res.result == "1") {
						if (res.data) {
							this.userWish = res.data;
							//调用志愿排名
							this.getWishListInfo();
							//调用轮播图
							this.getAdListInfo();
						} else {
							uni.reLaunch({
								url: "/pages/volunteerRanking/volunteerApplicationStatistics",
							});
						}
					} else {
						this.$showToast(res.message || "查询失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败");
				});
		},
		//志愿排名
		getWishListInfo() {
			getWishList({
				pagNo: 1,
				pageSize: 9999,
				masterType: this.userWish.masterType,
			})
				.then(res => {
					if (res.result == "1") {
						this.wishList = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		// 获取轮播图数据
		getAdListInfo() {
			this.params.masterType = this.userWish.masterType;
			this.params.globalSchoolId = this.userWish.schoolProfessionId;
			this.params.advertType = 2;
			getAdList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.main {
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
	.main-top-image {
		margin-top: 26rpx;
		width: calc(100% - 80rpx);
		height: 282rpx;
		overflow: hidden;
		// background: #ce6464;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
	}
	// image {
	// 	margin-top: 26rpx;
	// 	width: calc(100% - 80rpx);
	// 	height: 282rpx;
	// 	background: #ce6464;
	// 	border-radius: 30rpx 30rpx 30rpx 30rpx;
	// }
	.table-box {
		margin-top: 30rpx;
		width: 100%;
		height: 100vh;
		background: #ffffff;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 1rpx solid #ebecf0;
		overflow: hidden;
		.table {
			margin-top: 20rpx;
			width: calc(100% - 41rpx);
			// height: 354rpx;
			background: #ffffff;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			.tr {
				width: calc(100%);
				height: 74rpx;
				background: #f6f7fb;
				border-radius: 16rpx 16rpx 0rpx 0rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				overflow: hidden;
				.tb {
					width: 100rpx;
					height: 74rpx;
					border: 1rpx solid #ebecf0;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #8590a7;
				}
				.tb-r {
					border-radius: 16rpx 0rpx 0rpx 0rpx;
				}
				.tb-l {
					border-radius: 0rpx 16rpx 0rpx 0rpx;
				}
			}
			.tbody {
				width: calc(100%);
				// height: 74rpx;
				min-height: 97rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.tbody-tr {
					width: calc(100%);
					// height: 74rpx;
					min-height: 97rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.tbody-tb {
						width: 100rpx;
						// height: 74rpx;
						min-height: 97rpx;
						border: 1rpx solid #ebecf0;
						display: flex;
						justify-content: center;
						align-items: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 24rpx;
						color: #1f2638;
					}
				}
			}
		}
	}
}
.clamp {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2 !important;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: center;
	width: 188rpx;
}
</style>
