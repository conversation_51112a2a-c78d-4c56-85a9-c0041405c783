<template>
	<view class="container">
		<view class="top">
			<u-navbar leftText="掌握情况" leftIconColor="#212838" autoBack title=" " :bgColor="bgColor" safeAreaInsetTop placeholder></u-navbar>
		</view>
		<view class="main" :style="'top:' + topHeight + 'px;'">
			<view class="search" @click="show = true">
				<view class="search-content">
					<text>业务课一</text>
				</view>
				<image :src="imagebaseurl + 'static/icon/arrow-down.png'" mode=""></image>
			</view>
			<view class="card">
				<view class="card-item" v-for="item in 5">
					<view class="card-item-top">
						<view class="card-item-left">
							<image :src="imagebaseurl + 'static/icon/book_icon_2.png'" mode=""></image>
							<text>法律史</text>
						</view>
						<text>40</text>
					</view>
					<view class="card-item-bottom">
						<view class="card-item-bottom-box" v-for="item in 2">
							<text class="card-item-bottom-left">已掌握</text>
							<view class="card-item-bottom-right">
								<text>10</text>
								<text>/</text>
								<text>40</text>
							</view>
						</view>
						<view class="card-item-bottom-box">
							<text class="card-item-bottom-left">未完全掌握</text>
							<view class="card-item-bottom-right">
								<text>10</text>
								<text>/</text>
								<text>40</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-picker :show="show" :columns="columns" confirmColor="#2168fe" @cancel="show = false" @confirm="show = false"></u-picker>
	</view>
</template>

<script>
import { imagebaseurl } from '../../api/index.js';
export default {
	data() {
		return {
			bgColor: 'rgba(255,255,255,0)',
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			show: false,
			columns: [['陕西省', '西北政法大学']]
		};
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select('.top')
				.boundingClientRect((data) => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log('元素的高度：', data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		}
	}
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	padding-bottom: 100rpx;
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	padding-bottom: 200rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}

.card {
	margin-top: 26rpx;
	width: calc(100% - 80rpx);
	padding-bottom: 50rpx;
	background: #ffffff;
	border-radius: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-item {
		margin-top: 40rpx;
		width: calc(100% - 60rpx);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
		.card-item-top {
			width: calc(100%);
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.card-item-left {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 16rpx;
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
		.card-item-bottom {
			padding-top: 10rpx;
			width: calc(100%);
			height: 240rpx;
			background: #f5f7fa;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.card-item-bottom-box {
				margin-top: 30rpx;
				width: calc(100% - 80rpx);
				display: flex;
				justify-content: space-between;
				align-items: center;
				.card-item-bottom-left {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 26rpx;
					color: #1f2638;
				}
				.card-item-bottom-right {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					text:first-child {
						color: #2168fe;
					}
					text {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 26rpx;
						color: #8590a7;
					}
				}
			}
		}
	}
}
.line {
	width: 2rpx;
	height: 28rpx;
	background: linear-gradient(180deg, rgba(123, 126, 154, 0) 0%, rgba(123, 126, 154, 0.5) 52%, rgba(123, 126, 154, 0) 100%);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
}
.search {
	width: calc(100% - 80rpx);
	height: 76rpx;
	background: rgba(31, 64, 133, 0.1);
	border-radius: 38rpx 38rpx 38rpx 38rpx;
	border: 2rpx solid #1f4085;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.search-content {
		margin-left: 30rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 12rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #212838;
		}
	}
	image {
		margin-right: 20rpx;
		width: 24rpx;
		height: 24rpx;
	}
}
</style>
