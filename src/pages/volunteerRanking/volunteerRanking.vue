<template>
	<!-- 志愿排名 -->
	<view class="container">
		<image
			class="top"
			:src="imagebaseurl + 'static/icon/volunteerRankiBackground.png'"
			mode=""></image>
		<view class="content">
			<view
				class="card"
				@click="getVolunteerApplication()">
				<image
					class="card-image"
					:src="imagebaseurl + 'static/icon/volunteer_icon_1.png'"
					mode=""></image>
				<view class="card-content">
					<view class="card-text">
						<text class="text-1">志愿填报</text>
						<text class="text-2">志愿填报与统计</text>
					</view>
					<image
						class="icon"
						:src="imagebaseurl + 'static/icon/right_blue.png'"
						mode=""></image>
				</view>
			</view>
			<view
				class="card"
				@click="getExamRankingQuery">
				<image
					class="card-image"
					:src="imagebaseurl + 'static/icon/volunteer_icon_2.png'"
					mode=""></image>
				<view class="card-content">
					<view class="card-text">
						<text class="text-1">在线排名</text>
						<text class="text-2">上传成绩生成排名</text>
					</view>
					<image
						class="icon"
						:src="imagebaseurl + 'static/icon/right_orange.png'"
						mode=""></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { queryUserScore, getUserWish } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			imagebaseurl: imagebaseurl,
			scoreList: [],
			openId: uni.getStorageSync("openId"),
			currYear: "",
		};
	},
	onLoad() {
		let curr = new Date().getFullYear();
		this.currYear = curr;
		wx.showShareMenu({
			title: "立格致远教育",
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
		});
	},
	onShareAppMessage() {
		return {
			title: `立格致远教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},
	methods: {
		gotoLogin() {
			const token = uni.getStorageSync("token");
			if (token == null || token == undefined || token == "") {
				uni.showModal({
					title: "提示",
					content: "您还未登录，请先登录",
					showCancel: true,
					cancelText: "暂不登录",
					success: function (res) {
						if (res.confirm) {
							uni.reLaunch({
								url: "/pages/login/phoneLogin",
							});
						}
					},
				});
				return;
			}
		},
		getVolunteerApplication() {
			uni.setStorageSync("startPage", uni.$u.page());
			let openId = uni.getStorageSync("openId");
			if (openId) {
				this.openId = openId;
				this.getUserWishResp();
			}else{
				uni.navigateTo({
					url: "/pages/volunteerRanking/volunteerApplicationStatistics?from=tab",
				});
			}
		},
		getUserWishResp() {
			getUserWish({ openid: this.openId })
				.then(res => {
					if (res.result == "1") {
						if (uni.$u.test.object(res.data) && Object.keys(res.data).length > 0) {
							uni.reLaunch({
								url: "/pages/volunteerRanking/volunteerStatistics",
							});
						} else {
							uni.navigateTo({
								url: "/pages/volunteerRanking/volunteerApplicationStatistics",
							});
						}
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		getExamRankingQuery() {
			uni.setStorageSync("startPage", uni.$u.page());
			let openId = uni.getStorageSync("openId");
			if (openId) {
				this.openId = openId;
				this.queryUserScoreResp();
			}else{
				uni.navigateTo({
					url: "/pages/scoreRanking/examRankingQuery?from=tab",
				});
			}
		},

		queryUserScoreResp() {
			// 查询用户成绩历史
			queryUserScore({
				openId: this.openId,
			}).then(res => {
					if (res.result == "1") {
						this.scoreList = res.data;
						if (this.scoreList.length > 0) {
							let year = this.scoreList[0].year;
							if (parseInt(year) == this.currYear) {
								this.$store.commit("updateScoreList", this.scoreList);
								this.$store.commit("updateOpenId", openId);
								uni.navigateTo({
									url: "/pages/scoreRanking/scoreRanking",
								});
							} else {
								uni.navigateTo({
									url: "/pages/scoreRanking/examRankingQuery",
								});
							}
						} else {
							uni.navigateTo({
								url: "/pages/scoreRanking/examRankingQuery",
							});
						}
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		}
	},
};
</script>

<style lang="scss">
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.top {
	width: 100%;
	height: 400rpx;
	z-index: 9;
}
.content {
	width: 100%;
	height: calc(100vh - 400rpx);
	background: #f6f7fb;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	position: relative;
	top: -36rpx;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.card {
	margin-top: 30rpx;
	width: calc(100% - 80rpx);
	height: 160rpx;
	.card-image {
		width: calc(100% - 80rpx);
		height: 160rpx;
		position: absolute;
		z-index: -1;
	}
	.card-content {
		position: relative;
		left: 203rpx;
		width: calc(100% - 203rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;

		.icon {
			margin-right: 40rpx;
			width: 40rpx;
			height: 40rpx;
		}
		.card-text {
			width: 224rpx;
			height: 160rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			gap: 10rpx;

			.text-1 {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #212838;
			}
			.text-2 {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #8590a7;
			}
		}
	}
}
</style>
