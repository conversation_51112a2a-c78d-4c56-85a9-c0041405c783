<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftIconColor="#212838"
				@leftClick="letfClick"
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="mian-top">
				<text>志愿填报</text>
				<image
					:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon.png'"
					mode=""></image>
			</view>
			<view class="card">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon_1.png'"
						mode=""></image>
					<text>基本信息</text>
				</view>
				<u-line
					length="100%"
					margin="20rpx 0rpx"></u-line>
				<view class="card-content-box">
					<text> <text style="color: red">*</text> 昵称</text>
					<view class="input-box">
						<u--input
							placeholder="请输入昵称"
							v-model="user.nickName"
							@change="btnBClick"
							border="none"></u--input>
					</view>
				</view>
				<view class="card-content-box-radion">
					<text>学习形式</text>
					<view class="input-box-radion">
						<view
							class="radion-box"
							@click="seleccMasytype(1)">
							<image
								v-if="user.masterType == 1"
								:src="imagebaseurl + 'static/icon/radion-select.png'"
								mode=""></image>
							<image
								v-if="user.masterType == 2"
								:src="imagebaseurl + 'static/icon/radion-noselect.png'"
								mode=""></image>
							<text :class="user.masterType == 1 ? 'text-active' : 'text'">学硕</text>
						</view>
						<view
							class="radion-box"
							style="margin-left: 30rpx"
							@click="seleccMasytype(2)">
							<image
								v-if="user.masterType == 2"
								:src="imagebaseurl + 'static/icon/radion-select.png'"
								mode=""></image>
							<image
								v-if="user.masterType == 1"
								:src="imagebaseurl + 'static/icon/radion-noselect.png'"
								mode=""></image>
							<text :class="user.masterType == 2 ? 'text-active' : 'text'">专硕</text>
						</view>
					</view>
				</view>
				<view
					class="card-content-box"
					v-for="(item, index) in arrayList"
					:key="item.id">
					<text>{{ item.name }}</text>
					<view
						class="input-box"
						@click="openPicker(index)">
						<text>{{ item.content }}</text>
						<image
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
			</view>

			<button
				class="btn"
				style="color: white"
				open-type="getPhoneNumber"
				v-if="(user.phone == null || user.phone == '') && (pageFrom != 'tab')"
				@getphonenumber="submit">
				立即提交
			</button>
			<view
				v-else
				class="btn"
				@click="submit">
				<text>立即提交</text>
			</view>
		</view>
		<view v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				:keyName="keyName"
				confirmColor="#2168fe"
				:defaultIndex="[defaultIndex]"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import {
	getSchoolCategoryList,
	querySchoolList,
	querySchoolProfessionList,
	querySchoolDepartmentList,
	saveUserWish,
	getUserWish,
	requestOpenId,
	checkNickName,
	goLogin,
	requestEncryptedData,
} from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			user: {
				nickName: "",
				phone: "",
				masterType: 1,
			},
			cardType: 0,
			arrayList: [
				{ id: 1, name: "报考分类", content: "全部" },
				{ id: 2, name: "报考院校", content: "请选择院校" },
				{ id: 3, name: "报考院系", content: "请选择院系" },
				{ id: 4, name: "报考专业", content: "请选择专业" },
			],
			show: false,
			columns: [[]],
			//选择的arrayList
			selectCollege: 0,
			// 分类
			schoolCategory: [],
			selectSchoolCategory: {
				classificationName: "全部",
				id: -1,
			},
			selectIndex: 0,
			//学校
			schools: [],
			selectedSchool: {},
			//院系
			schoolDepartmentList: [],
			selectedSchoolDepartment: {},
			//专业
			schoolProfessionList: [],
			selectedMajor: {},
			keyName: "",
			defaultIndex: 0,
			masterType: 1,
			openId: "",
			sessionKey: "",
			checkNickName: true,
			pageFrom: '',
		};
	},
	onLoad(option) {
		let self = this;
		// 获取缓存用户信息
		let userInfo = uni.getStorageSync("user");
		if (userInfo) {
			this.user.masterType = userInfo.masterType;
			this.user.nickName = userInfo.nickName;
			this.user.phone = userInfo.phone;
			this.openId = userInfo.openId;
		}

		this.pageFrom = option.from || '';

		if (this.openId) {
			this.initData();
		} else {
			uni.login({
				provider: "weixin",
				success: function (loginRes) {
					self.requestOpenIdResp(loginRes.code);
				},
			});
		}

		wx.showShareMenu({
			title: "立格致远教育",
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
		});
	},
	onShareAppMessage() {
		return {
			title: `立格致远教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/volunteerRanking/volunteerApplicationStatistics",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		initData() {
			// this.getUserInfo();
			this.getSchoolCategory();
			this.querySchoolListResp();
			getUserWish({ openid: this.openId })
				.then(res => {
					if (res.result == "1") {
						if (uni.$u.test.object(res.data) && Object.keys(res.data).length > 0) {
							uni.reLaunch({
								url: "/pages/volunteerRanking/volunteerStatistics",
							});
						}
					} else {
						this.$showToast(res.message || "查询失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败");
				});
		},
		btnBClick() {
			// 此处用法为在js中调用，需要写uni.$u.debounce()
			uni.$u.debounce(this.toNext, 500);
		},
		toNext() {
			checkNickName({ nickName: this.user.nickName, type: 2 }).then(res => {
				console.log(res);
				this.checkNickName = res.data;
				if (!this.checkNickName) {
					this.$showToast("昵称已存在");
				}
			});
		},
		letfClick() {
			let startPage = uni.getStorageSync("startPage");
			if (uni.getStorageSync("startPage") && startPage !== "") {
				uni.switchTab({
					url: uni.getStorageSync("startPage"),
				});
			} else {
				uni.reLaunch({
					url: "/pages/login/phoneLogin",
				});
			}
		},
		submit(event) {
			if (!this.checkNickName) {
				this.$showToast("昵称已存在");
				return;
			}
			if (Object.keys(this.selectedSchool).length == 0) {
				this.$showToast("请选择学校");
				return;
			}
			if (Object.keys(this.selectedSchoolDepartment).length == 0) {
				this.$showToast("请选择院系");
				return;
			}
			if (Object.keys(this.selectedMajor).length == 0) {
				this.$showToast("请选择专业");
				return;
			}
			if (!this.user.nickName) {
				this.$showToast("请输入用户昵称");
				return;
			}
			if (this.user.phone) {
				this.saveUser();
			} else {
				if (this.pageFrom === 'tab'){
					uni.showModal({
						title: "提示",
						content: "您还未登录，请先登录",
						showCancel: true,
						cancelText: "暂不登录",
						success: function (res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/phoneLogin",
								});
							}
						},
					});
					return;
				}
				this.requestEncryptedDataResp(event.detail.encryptedData, event.detail.iv);
			}
		},
		saveUser() {
			let params = {
				schoolId: this.selectedSchool.id,
				school: this.selectedSchool.school,
				schoolDepartmentId: this.selectedSchoolDepartment.id,
				schoolDepartment: this.selectedSchoolDepartment.schoolDepartment,
				schoolProfessionId: this.selectedMajor.id,
				schoolProfession: this.selectedMajor.schoolProfession,
				masterType: this.user.masterType,
				nickName: this.user.nickName,
				openid: this.openId,
				phone: this.user.phone,
			};
			if (Object.keys(this.selectSchoolCategory).length > 0) {
				params.classificationId = this.selectSchoolCategory.id;
				params.classificationName = this.selectSchoolCategory.classificationName;
			}
			saveUserWish(params)
				.then(res => {
					if (res.result == "1") {
						this.$showToast("提交成功");
						uni.reLaunch({
							url: "/pages/volunteerRanking/volunteerStatistics",
						});
					} else {
						this.$showToast(res.message || "提交失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "提交失败请重试");
				});
		},
		openPicker(index) {
			this.selectIndex = index;
			if (index == 0) {
				if (Object.keys(this.schoolCategory).length > 0) {
					this.defaultIndex = this.schoolCategory.findIndex(item => item.id == this.selectSchoolCategory.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex++;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = [];
				this.columns[0].push({
					classificationName: "全部",
					id: -1,
				});
				this.columns[0] = [...this.columns[0], ...this.schoolCategory];
				this.keyName = "classificationName";
			}
			if (index == 1) {
				if (this.schools.length == 0) {
					this.$showToast("暂无更多学校");
					return;
				}
				if (Object.keys(this.selectedSchool).length > 0) {
					this.defaultIndex = this.schools.findIndex(item => item.id == this.selectedSchool.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schools;
				this.keyName = "school";
			}
			if (index == 2) {
				if (Object.keys(this.selectedSchool).length == 0) {
					this.$showToast("请先选择学校");
					return;
				}
				if (Object.keys(this.selectedSchoolDepartment).length > 0) {
					this.defaultIndex = this.schoolDepartmentList.findIndex(item => item.id == this.selectedSchoolDepartment.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schoolDepartmentList;
				this.keyName = "schoolDepartment";
			}
			if (index == 3) {
				if (Object.keys(this.selectedSchoolDepartment).length == 0) {
					this.$showToast("请先选择院系");
					return;
				}
				if (Object.keys(this.selectedMajor).length > 0) {
					this.defaultIndex = this.schoolProfessionList.findIndex(item => item.id == this.selectedMajor.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schoolProfessionList;
				this.keyName = "schoolProfession";
			}
			this.show = true;
		},
		confirm(e) {
			if (this.keyName == "classificationName") {
				this.schoolList = {};
				this.selectSchoolDepartment = {};
				this.selectSchoolProfession = {};
				this.arrayList[3].content = "请选择拟报专业";
				this.arrayList[2].content = "请选择院系";
				this.arrayList[1].content = "请选择院校";
				this.arrayList[0].content = e.value[0].classificationName;
				this.selectSchoolCategory = e.value[0];

				this.querySchoolListResp();
			}
			if (this.keyName == "school") {
				this.arrayList[1].content = e.value[0].school;
				this.selectedSchool = e.value[0];
				this.selectSchoolDepartment = {};
				this.arrayList[3].content = "请选择拟报专业";
				this.arrayList[2].content = "请选择院系";

				this.querySchoolDepartmentListResp();
			}
			if (this.keyName == "schoolDepartment") {
				this.arrayList[2].content = e.value[0].schoolDepartment;
				this.selectedSchoolDepartment = e.value[0];
				this.selectSchoolProfession = {};
				this.arrayList[3].content = "请选择拟报专业";
				this.querySchoolProfessionListResp();
			}
			if (this.keyName == "schoolProfession") {
				this.arrayList[3].content = e.value[0].schoolProfession;
				this.selectedMajor = e.value[0];
			}
			this.show = false;
		},
		seleccMasytype(index) {
			// this.masterType = index;
			this.user.masterType = index;
			this.arrayList = [
				{ id: 1, name: "报考分类", content: "请选择分类" },
				{ id: 2, name: "报考院校", content: "请选择院校" },
				{ id: 3, name: "报考院系", content: "请选择院系" },
				{ id: 4, name: "报考专业", content: "请选择专业" },
			];
			this.selectSchoolCategory = {};
			this.schoolList = {};
			this.selectSchoolDepartment = {};
			this.selectSchoolProfession = {};
			this.getSchoolCategory();
			this.querySchoolListResp();
		},
		getSchoolCategory() {
			getSchoolCategoryList({ masterType: this.user.masterType })
				.then(res => {
					if (res.result == "1") {
						this.schoolCategory = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},

		// 查询学校
		querySchoolListResp() {
			let params = {
				masterType: this.user.masterType,
			};
			if (Object.keys(this.selectSchoolCategory).length > 0 && this.selectSchoolCategory.id != -1) {
				params.classificationId = this.selectSchoolCategory.id;
			}
			querySchoolList(params)
				.then(res => {
					if (res.result == "1") {
						this.schools = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		// 查询院系
		querySchoolDepartmentListResp() {
			querySchoolDepartmentList({
				schoolId: this.selectedSchool.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.schoolDepartmentList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		// 查询专业
		querySchoolProfessionListResp() {
			querySchoolProfessionList({
				schoolDepartmentId: this.selectedSchoolDepartment.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.schoolProfessionList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		// 获取openid
		requestOpenIdResp(code) {
			requestOpenId({
				code: code,
			})
				.then(res => {
					if (res.result == "1") {
						console.log("res: ", res.result == "1");
						console.log("this: ", this);
						this.openId = res.data.openid;

						uni.setStorageSync("openId", this.openId);
						this.sessionKey = res.data.sessionKey;
						this.$store.commit("updateOpenId", this.openId);
						// this.getUserInfo();
						this.initData();
					} else if (res.result == "200123") {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err || "授权失败，请重试");
				});
		},
		getUserInfo() {
			goLogin({ openId: this.openId })
				.then(res => {
					console.log("res: ", res);
					if (res.result == "1") {
						this.user = res.data;
						this.user.openId = this.openId;
						uni.removeStorageSync("user");
						uni.removeStorageSync("token");
						uni.setStorageSync("openId", this.openId);
						uni.setStorageSync("user", this.user);
						uni.setStorageSync("token", res.data.token);
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		// 请求解密手机号
		requestEncryptedDataResp(encryptedData, iv) {
			requestEncryptedData({
				sessionKey: this.sessionKey,
				encryptedData: encryptedData,
				iv: iv,
			})
				.then(res => {
					if (res.result == "1") {
						let data = JSON.parse(res.data);
						this.user.phone = data.phoneNumber;
						this.saveUser();
					} else {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "授权失败，请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		// openPicker(item) {
		// 	this.selectCollege = item.id;
		// 	//省份
		// 	if (item.id == 1) {
		// 		this.columns[0] = this.schoolTreeInfo;
		// 	}
		// 	//院校
		// 	if (item.id == 2) {
		// 		if (Object.keys(this.selectSchoolInfo).length == 0) {
		// 			this.$showToast('请先选择省份');
		// 			return;
		// 		}
		// 		this.columns[0] = this.selectSchoolInfo.children;
		// 	}
		// 	//院系
		// 	if (item.id == 3) {
		// 		if (Object.keys(this.selectSchoolchildren).length == 0) {
		// 			this.$showToast('请先选择院校');
		// 			return;
		// 		}
		// 		this.columns[0] = this.selectSchoolchildren.children;
		// 	}
		// 	//专业
		// 	if (item.id == 4) {
		// 		if (Object.keys(this.selectSchoolchildrenchildren).length == 0) {
		// 			this.$showToast('请先选择院系');
		// 			return;
		// 		}
		// 		this.columns[0] = this.selectSchoolchildrenchildren.children;
		// 	}
		// 	this.show = true;
		// },

		// confirm(e) {
		// 	if (e.value[0]) {
		// 		this.arrayList[this.selectCollege - 1].content = e.value[0].bizName;
		// 		if (this.selectCollege == 1) {
		// 			//省份
		// 			this.selectSchoolInfo = e.value[0];
		// 		}
		// 		if (this.selectCollege == 2) {
		// 			//院校
		// 			this.selectSchoolchildren = e.value[0];
		// 		}
		// 		if (this.selectCollege == 3) {
		// 			//院系
		// 			this.selectSchoolchildrenchildren = e.value[0];
		// 		}
		// 		if (this.selectCollege == 4) {
		// 			//专业
		// 			this.selectSchoolchildrenchildrenchildren = e.value[0];
		// 		}
		// 	}
		// 	this.show = false;
		// }
	},
};
</script>

<style lang="scss">
::v-deep .u-popup__content {
	border-radius: 30rpx;
}
::v-deep .u-toolbar {
	border-bottom: 1rpx solid #edeef2;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 36rpx;
}
.card-content-box-radion {
	margin-top: 20rpx;
	width: calc(100% - 68rpx);
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #646d80;
	}
	.input-box-radion {
		width: calc(100% - 22rpx);
		height: 70rpx;
		background: white;
		border-radius: 10rpx 10rpx 10rpx 10rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.radion-box {
			width: 200rpx;
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			.text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #8590a7;
			}
			.text-active {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #1f2638;
			}

			image {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}
}
.container {
	width: 100%;
	height: 100vh;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	// background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	background: linear-gradient(180deg, #bbddfa 8%, #f6f7fb 35%);
}
.top {
	width: 100%;
	// background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	background: linear-gradient(180deg, #bbddfa 114%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	.mian-top {
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #1f2638;
		}
		image {
			width: 168rpx;
			height: 148rpx;
			margin-right: 26rpx;
		}
	}
	.card {
		position: relative;
		top: -1rpx;
		width: calc(100% - 60rpx);
		// height: 1000rpx;
		padding-bottom: 40rpx;
		background: #ffffff;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.card-top {
			margin-top: 20rpx;
			width: calc(100% - 77rpx);
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 20rpx;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f4085;
			}
			image {
				width: 27.63rpx;
				height: 23.49rpx;
			}
		}
		.card-content-box {
			margin-top: 40rpx;
			width: calc(100% - 68rpx);
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #646d80;
			}
			.input-box {
				padding-left: 22rpx;
				width: calc(100% - 22rpx);
				height: 70rpx;
				background: #f5f7fa;
				border-radius: 10rpx 10rpx 10rpx 10rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #1f2638;
				}
				image {
					width: 10.65rpx;
					height: 18.63rpx;
					margin-right: 30rpx;
				}
			}
		}
	}
	.btn {
		margin-top: 52rpx;
		width: calc(100% - 64rpx);
		height: 98rpx;
		background: #2168fe;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #ffffff;
		}
	}
}
</style>
