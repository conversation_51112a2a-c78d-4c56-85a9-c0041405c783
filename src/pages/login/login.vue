<template>
	<view class="container">
		<!-- <button @click="goHome()">登录</button> -->
		<view class="logo">
			<image
				:src="imagebaseurl + 'static/icon/login_icon.png'"
				mode=""></image>
			<text>立 人 格 物 · 行 稳 致 远</text>
		</view>
		<view class="btn-box">
			<view
				class="btn blue"
				@click="phoneLogin()">
				<image
					:src="imagebaseurl + 'static/icon/phone_icon.png'"
					mode=""></image>
				<text>手机号登录</text>
			</view>
			<view
				class="btn green"
				@click="goHome">
				<image
					:src="imagebaseurl + 'static/icon/weixi_icon.png'"
					mode=""></image>
				<text>微信登录</text>
			</view>
		</view>
	</view>
</template>
<script>
import { requestOpenId, goLogin } from "../../api/api";
import { imagebaseurl } from "../../api/index.js";
export default {
	data() {
		return {
			imagebaseurl: imagebaseurl,
			openid: "",
		};
	},
	onLoad(query) {
		// this.requestOpenIdResp();
	},
	mounted() {},
	methods: {
		// 获取openid
		requestOpenIdResp() {
			let _that = this;
			uni.login({
				provider: "weixin",
				success: function (event) {
					const { code } = event;
					//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
					requestOpenId({
						code: code,
					})
						.then(res => {
							if (res.result == "1") {
								_that.$store.commit("updateOpenId", res.data.openid);
								_that.openid = res.data.openid;
								goLogin({ openId: res.data.openid }).then(res => {
									console.log("登录信息：", res);
									if (res.result == "1") {
										uni.setStorageSync("openId", res.data.openid);
										uni.setStorageSync("user", res.data);
										uni.setStorageSync("token", res.data.token);
										uni.reLaunch({
											url: "/pages/home/<USER>",
										});
									} else if (res.result == "200123") {
										_that.$showToast(res.message || "登录失败，请重试");
									}
								});
							}
						})
						.catch(err => {
							_that.$showToast(err.message || "授权失败，请重试");
						});
				},
				fail: function (err) {
					// 登录授权失败
					_that.$showToast(err.message || "授权失败，请重试");
				},
			});
		},
		goHome() {
			this.requestOpenIdResp();
		},
		submitLogin() {
			goLogin({ openId: this.openid }).then(res => {
				if (res.result != "1") {
					setTimeout(() => {
						uni.navigateTo({
							url: "/pages/register/register",
						});
					}, 1000);
				} else if (res.result == "200123") {
					_that.$showToast(res.message || "登录失败，请重试");
				} else {
					uni.setStorageSync("openId", this.openid);
					uni.setStorageSync("user", res.data);
					uni.setStorageSync("token", res.data.token);
					uni.reLaunch({
						url: "/pages/home/<USER>",
					});
				}
			});
		},
		phoneLogin() {
			uni.navigateTo({
				url: "/pages/login/phoneLogin",
			});
		},
	},
};
</script>
<style lang="scss">
.container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(180deg, #bbddfa 0%, #ffffff 67%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 164rpx;
}
.logo {
	width: calc(100% - 300rpx);
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 18rpx;
	image {
		width: 239.07rpx;
		height: 223.32rpx;
	}
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 30rpx;
		color: #1f2638;
		line-height: 66rpx;
	}
}
.btn-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 80rpx;
	.btn {
		width: calc(100% - 150rpx);
		height: 98rpx;
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 18rpx;
		image {
			width: 52rpx;
			height: 52rpx;
		}
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #ffffff;
		}
	}
	.blue {
		background: #2168fe;
	}
	.green {
		background: #2ad473;
	}
}
</style>