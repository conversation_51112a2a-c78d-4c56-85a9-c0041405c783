<template>
	<view class="container">
		<u-navbar
			title=" "
			bgColor="rgba(255,255,255,0)"
			leftIconSize="0"
			placeholder>
			<view
                class="u-nav-slot"
                slot="left"
            >
                <u-icon
					v-if="showBack"
                    name="arrow-left"
                    size="19"
					@click="goBack"
                ></u-icon>
            </view>
		</u-navbar>
		<image
			class="image"
			:src="imagebaseurl + 'static/icon/phone_login.png'"
			mode=""></image>
		<view class="card">
			<view class="card-content-box">
				<view class="title-text">
					<image
						src="../../static/icon/phone-icon.png"
						mode=""></image>
					<text>手机号</text>
				</view>
				<view class="input-box">
					<u--input
						placeholder="请输入手机号"
						border="none"
						v-model="phone"></u--input>
				</view>
			</view>
			<view class="card-content-box">
				<view class="title-text">
					<image
						src="../../static/icon/number-icon.png"
						mode=""></image>
					<text>验证码</text>
				</view>
				<view class="number-box">
					<u--input
						placeholder="请输入验证码"
						border="none"
						v-model="number"></u--input>
					<view
						class="send-btn"
						:style="countingDown ? 'background:rgb(33 104 254 / 50%)' : ''"
						@click="sendCodes">
						<text> {{ countingDown ? `${countdown}秒` : "发送验证码" }}</text>
					</view>
				</view>
			</view>
			<view class="agreement">
				<view class="agreement-text">
					<view
						class="agreement-btn"
						@click="selectAgreement()">
						<image
							v-if="checkboxValue == true"
							class="image-btn"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							v-if="checkboxValue == false"
							class="image-btn"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
						<text>我已阅读并同意</text>
					</view>
					<text
						style="color: #2168fe"
						@click="goAgreemen('https://qnpb.ligeedu.cn/law-userAgreement.html')"
						>《用户协议》</text
					>
					<text>和</text>
					<text
						style="color: #2168fe"
						@click="goAgreemen('https://qnpb.ligeedu.cn/law-privacyClause.html')"
						>《隐私政策》</text
					>
				</view>
			</view>

			<view
				class="submitview"
				@click="submitPhone">
				<text>立即登录</text>
			</view>

			<view class="foot">
				<view class="divide">
					<u-divider text="微信快捷登录"></u-divider>
				</view>
				<image
					@click="wechatLogin"
					class="login-method"
					:src="imagebaseurl + 'static/icon/login-fast-icon.png'"
					mode=""></image>
			</view>
		</view>

		<u-picker
			:show="show"
			:columns="columns"
			:keyName="keyName"
			@cancel="show = false"
			@confirm="confirm"></u-picker>

		<van-popup
			:show="showUserList"
			round
			position="bottom"
			
		>
			<view class="user-list-view">
				<view class="user-phone-item" style="font-size: 30rpx; color: #c9c9c9;">选择登录用户</view>
				<view class="user-phone-item" v-for="userItem in userList" :key="userItem.phone" @click="phoneLoginResp(userItem.phone)">
					{{ userItem.phone }}
				</view>
				<view @click="showUserList = false" class="user-phone-item">取消</view>
			</view>
		</van-popup>
	</view>
</template>

<script>
import { sendCode, phoneLogin, requestOpenId, goLogin, queryUserList } from "../../api/api.js";
import { imagebaseurl } from "../../api/index.js";
export default {
	data() {
		return {
			user: {},
			imagebaseurl: imagebaseurl,
			phone: "",
			number: "",
			tip: "发送验证码",
			checkboxValue: false,
			countdown: 60, // 倒计时时长
			countingDown: false, // 是否正在倒计时
			openId: "",
			userList: [],
			showUserList: false,

			newVersionObj: {},
			showBack: false,
		};
	},
	created() {
		this.newVersionObj = this.$store.state.appNewVersionObj;
		if (this.newVersionObj.versionNo === this.$VERSION && this.newVersionObj.publishStatus == 2) {
			this.showBack = true;
		}
		this.requestOpenIdResp();
	},
	methods: {
		goBack() {
			uni.switchTab({ url: "/pages/home/<USER>" });
		},
		selectAgreement() {
			this.checkboxValue = !this.checkboxValue;
		},
		btnBClick() {
			// 此处用法为在js中调用，需要写uni.$u.debounce()
			uni.$u.debounce(this.submitPhone, 1000);
		},
		goAgreemen(url) {
			uni.navigateTo({
				url: "/pages/webview/webview?url=" + url,
			});
		},
		// 获取openid
		requestOpenIdResp() {
			let _that = this;
			uni.login({
				provider: "weixin",
				success: function (event) {
					const { code } = event;
					_that.autoLogin(code);
				},
				fail: function (err) {
					// 登录授权失败
					_that.$showToast(err.message || "授权失败，请重试");
				},
			});
		},
		autoLogin(code) {
			let _that = this;
			//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
			requestOpenId({
				code: code,
			})
			.then(res => {
				if (res.result == "1") {
					_that.$store.commit("updateOpenId", res.data.openid);
					_that.openId = res.data.openid;
					_that.selectedPhoneLogin();
				}else{
					_that.$showToast(res.message || "授权失败，请重试");
				}
			})
			.catch(err => {
				_that.$showToast(err.message || "授权失败，请重试");
			});
		},
		sendCodes() {
			// 在这里添加发送验证码的逻辑
			if (this.phone == "") {
				this.$showToast("请输入手机号");
				return;
			}
			if (!uni.$u.test.mobile(this.phone)) {
				this.$showToast("请输入正确的手机号");
				return;
			}
			sendCode({ phone: this.phone })
				.then(res => {
					console.log(res);
					if (res.result == "1") {
						this.$showToast("发送成功");
						this.countingDown = true;
						const timer = setInterval(() => {
							this.countdown -= 1;
							if (this.countdown === 0) {
								clearInterval(timer);
								this.countingDown = false;
								this.countdown = 60; // 重置倒计时时长
							}
						}, 1000);
					} else {
						this.$showToast(res.message || "发送失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "发送失败");
				});
		},
		submitPhone() {
			if (!this.checkboxValue) {
				this.$showToast("请同意用户协议和隐私政策");
				return;
			}
			if (this.phone == "") {
				this.$showToast("请输入手机号");
				return;
			}
			if (!uni.$u.test.mobile(this.phone)) {
				this.$showToast("请输入正确的手机号");
				return;
			}
			if (this.number == "") {
				this.$showToast("请输入验证码");
				return;
			}

			phoneLogin({
				phone: this.phone,
				verificationCode: this.number,
				role: 1,
				openId: this.openId,
			})
				.then(res => {
					console.log("res: ", res);
					if (res.result == "1") {
						uni.setStorageSync("user", res.data);
						uni.setStorageSync("token", res.data.token);
						uni.reLaunch({
							url: "/pages/home/<USER>",
						});
					} else if (res.result == "200123") {
						_that.$showToast(res.message || "登录失败，请重试");
					} else {
						uni.navigateTo({
							url: "/pages/register/register",
						});
					}
				})
				.catch(err => {
					_that.$showToast(err.message || "登录失败，请重试");
				});
		},
		selectedPhoneLogin(gotoRegister) {
			let _that = this;
			// todo: 查询openid 对应几个手机号账号
			queryUserList({
				openId: this.openId,
			}).then(res => {
				if (res.result == "1") {
					// 
					_that.userList = res.data;
					if (!res.data || res.data.length == 0) {
						if (gotoRegister) {
							uni.navigateTo({
								url: "/pages/register/register",
							});
						}
					}else{
						if (res.data.length == 1) {
							this.phoneLoginResp(res.data[0].phone);
						}else{
							// 显示选择用户
							this.showUserList = true;
						}
					}
				}else{
					_that.$showToast(res.message || "登录失败，请重试");
				}
			}).catch(err => {
				_that.$showToast(err.message || "登录失败，请重试");
			});
		},
		wechatLogin() {
			if (!this.checkboxValue) {
				this.$showToast("请同意用户协议和隐私政策");
				return;
			}
			this.selectedPhoneLogin(true);
		},

		phoneLoginResp(phone) {
			let _that = this;
			uni.removeStorageSync("openId");
			uni.setStorageSync("openId", _that.openId);
			phoneLogin({
				openId: this.openId,
				role: 1,
				phone: phone,
			})
			.then(res => {
				console.log("res: ", res);
				if (res.result == "1") {
					uni.setStorageSync("user", res.data);
					uni.setStorageSync("token", res.data.token);
					uni.reLaunch({
						url: "/pages/home/<USER>",
					});
				} else if (res.result == "200123") {
					_that.$showToast(res.message || "登录失败，请重试");
				} else {
					uni.navigateTo({
						url: "/pages/register/register",
					});
				}
			})
			.catch(err => {
				_that.$showToast(err.message || "登录失败，请重试");
			});
		}
	},
};
</script>

<style lang="scss">
.container {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: fixed;
}
.image {
	position: absolute;
	top: 0;
	width: 100%;
	height: 1043rpx;
}
.foot {
	position: fixed;
	bottom: 30rpx;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.divide {
	width: calc(100% - 300rpx);
}
.login-method {
	width: calc(100% - 300rpx);
	border-radius: 88rpx;
	width: 88rpx;
	height: 88rpx;
}
.agreement {
	margin-top: 80rpx;
	width: calc(100% - 130rpx);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	.agreement-text {
		display: flex;
		align-items: center;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 22rpx;
		.agreement-btn {
			display: flex;
			align-items: center;
			gap: 9rpx;
			.image-btn {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}
}

.card {
	width: 100%;
	height: 1142rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	border: 2rpx solid #ffffff;
	z-index: 99;
	position: relative;
	top: 306rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.submitview {
		margin-top: 34rpx;
		width: calc(100% - 130rpx);
		height: 98rpx;
		background: #2168fe;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		color: #ffffff;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
.card-content-box {
	margin-top: 40rpx;
	width: calc(100% - 130rpx);
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	.title-text {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 10rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #646d80;
		image {
			width: 21.04rpx;
			height: 25.31rpx;
		}
	}

	.input-box {
		padding-left: 22rpx;
		width: calc(100% - 22rpx);
		height: 80rpx;
		background: #f5f7fa;
		border-radius: 10rpx 10rpx 10rpx 10rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.number-box {
		padding-left: 22rpx;
		width: calc(100% - 22rpx);
		height: 80rpx;
		background: #f5f7fa;
		border-radius: 10rpx 10rpx 10rpx 10rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.send-btn {
			width: 180rpx;
			height: 60rpx;
			background: #2168fe;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 26rpx;
			color: #ffffff;
		}
	}
}
.user-list-view {
	width: '100vw';
	background-color: '#fff';
	.user-phone-item {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		font-size: 40rpx;
		text-align: center;
		border-bottom: #f1f1f1 1px solid;
	}
}
</style>