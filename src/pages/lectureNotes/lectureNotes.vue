<template>
	<view
		class="container"
		:style="leftMenu.length == 0 ? 'justify-content: center;align-items: center;' : ''">
		<!-- :style="leftMenu.length == 0 ? 'justify-content: center;align-items: center;' : ''" -->
		<view class="top">
			<u-navbar
				leftText="讲义背诵"
				title=" "
				:leftIconSize="0"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
			<view class="top-body">
				<view class="top-body-box">
					<text>历史记录：</text>
					<view
						class="historical-records"
						@click="historyClick">
						<text>{{ userSubject.name || "暂无" }}</text>
						<image
							:src="imagebaseurl + 'static/icon/edit.png'"
							mode=""></image>
					</view>
				</view>
				<image
					v-if="lettureNotes.typeSwitch == 0"
					class="unlock"
					:src="imagebaseurl + 'static/icon/unlock.png'"></image>
				<image
					v-if="lettureNotes.typeSwitch == 1"
					class="unlock"
					:src="imagebaseurl + 'static/icon/unlock_blue.png'"></image>
			</view>
			<view
				class="top-foot"
				:style="'opacity:' + opacity">
				<view class="top-foot-left">
					<image
						:src="imagebaseurl + 'static/icon/subject_icon.png'"
						mode=""></image>
					<text>科目章节</text>
				</view>
				<view class="top-foot-right">
					<image
						:src="imagebaseurl + 'static/icon/book_icon.png'"
						mode=""></image>
					<text>我的讲义</text>
				</view>
			</view>
		</view>
		<view v-if="leftMenu.length == 0">
			<u-empty
				mode="list"
				:icon="require('../../static/icon/order-empty.png')"
				text="暂无讲义"></u-empty>
		</view>
		<view
			v-if="leftMenu.length > 0"
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<scroll-view
				scroll-y="true"
				class="scroll-L"
				:style="'height:' + 'calc(100vh - ' + topHeight + 'px)'">
				<view
					class="leftMenu"
					v-for="(item, index) in leftMenu">
					<view
						:class="selectLeftItem.courseId == item.courseId ? 'leftMenu-title-active' : 'leftMenu-title'"
						@click="openLeftMenu(item)">
						<text>{{ item.courseName }}</text>
						<u-icon
							:name="selectLeftItem.courseId == item.courseId ? 'arrow-up' : 'arrow-down'"
							size="16.17px"></u-icon>
					</view>

					<view
						class="leftMenu-box"
						:style="selectLeftItem.courseId == item.courseId ? 'height:' + item.childen.length * 68 + 'rpx' : ''">
						<view
							:class="oldChildenItem.professionId == item2.professionId ? 'leftMenu-content active' : 'leftMenu-content'"
							v-for="(item2, index2) in item.childen"
							@click="openLeftMenuChilden(item2)">
							<text>{{ item2.professionName }}</text>
						</view>
					</view>
				</view>
				<view v-if="leftMenu.length == 0">
					<u-empty
						mode="list"
						:icon="require('../../static/icon/order-empty.png')"
						text="暂无章节目录"></u-empty>
				</view>
			</scroll-view>
			<scroll-view
				scroll-y="true"
				class="scroll-R"
				:style="'height:' + 'calc(100vh - ' + topHeight + 'px)'">
				<view
					class="rightMenu"
					v-for="(item, index) in rightMenu">
					<view
						:class="selectRighttItem.professionId == item.professionId ? 'rightMenu-title-active' : 'rightMenu-title'"
						@click="openRightMenu(item)">
						<view class="rightMenu-title-right">
							<image
								:style="selectRighttItem.professionId == item.professionId ? 'transform: rotate(90deg);' : ''"
								:src="imagebaseurl + 'static/icon/arrow_right_bold.png'"
								mode=""></image>

							<text>{{ item.professionName }}</text>
						</view>
						<!-- <u-icon :name="selectRighttItem == item.id ? 'arrow-up' : 'arrow-down'" size="16.17rpx"></u-icon> -->
					</view>
					<view
						:class="selectRighttItem.professionId == item.professionId ? 'rightMenu-box ' : 'rightMenu-box'"
						:style="selectRighttItem.professionId == item.professionId ? 'height:' + item.childen.length * 86 + 'rpx' : ' height: 0;'">
						<view
							:class="oldChildenRightItem.chapterId == item2.chapterId ? 'rightMenu-content active' : 'rightMenu-content'"
							v-for="(item2, index2) in item.childen"
							@click="openRightMenuChilden(item2, index2)">
							<view class="right-content-l">
								<text class="text">{{ item2.chapterName }}</text>
								<!-- 	<view class="sing">
									<text>付费</text>
								</view> -->
							</view>
							<view class="right-content-r">
								<text class="text-1">{{ item2.userSubjectCount }}</text>
								<text class="text-2">/</text>
								<text class="text-3">{{ item2.subjectCount }}</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getLectureCourseList, getlectureNotesProfessionList, getChapterList, getSwitchConfig, getUserInfo } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			user: uni.getStorageSync("user"),
			leftMenu: [],
			selectLeftItem: {},
			oldChildenItem: {},
			rightMenu: [],
			selectRighttItem: {},
			oldChildenRightItem: {},
			//图片基准地址
			imagebaseurl: imagebaseurl,
			params: {
				pageNo: 1,
				pageSize: 10,
			},
			userSubject: uni.getStorageSync("userSubject") || {},
			userConfig: [],
			lettureNotes: {},
			opacity: 1,
		};
	},
	created() {
		this.getUser();
		// 每次页面显示时都注册监听返回事件
		uni.$on("onBack", this.handleback);
		this.params.masterType = this.user.masterType;
		// this.params.globalSchoolId = this.user.globalSchoolId;
		// this.params.globalSchoolId = 1;
		// this.params.globalProfessionId = this.user.globalProfessionId;
		// this.params.globalProfessionId = 1;

		this.getLeftList();
	},

	mounted() {
		this.getElementHeight();
	},
	onShow() {
		getSwitchConfig({ orderType: 4 })
			.then(res => {
				console.log("用户订单状态：", res);
				if (res.result == "1") {
					this.userConfig = res.data;
					this.lettureNotes = res.data.find(item => item.orderType == 4);
				} else {
					this.$showToast(res.message || "配置异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "配置异常，请重试");
			});
	},
	beforeDestroy() {
		// 页面隐藏时注销监听
		uni.$off("onBack", this.handleback);
	},
	methods: {
		getUser() {
			getUserInfo({
				masterType: this.user.masterType,
				globalSchoolId: this.user.globalSchoolId,
				openId: this.openid,
			})
				.then(res => {
					if (res.result == "1") {
						this.user = res.data;
						uni.removeStorageSync("user");
						uni.setStorageSync("user", res.data);
					} else {
						this.$showToast(res.message || "获取用户信息失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取用户信息失败");
				});
		},
		handleback(data) {
			// 处理返回逻辑
			this.userSubject = uni.getStorageSync("userSubject") || {};
			//刷新菜单
			getChapterList(this.params)
				.then(res => {
					if (res.result == "1") {
						let rightInde1 = this.rightMenu.findIndex(item => item.professionId == this.selectRighttItem.professionId);
						this.rightMenu[rightInde1].childen = res.data.list;
					} else {
						this.$showToast(res.message || " 查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || " 查询失败，请重试");
				});
		},
		//历史记录
		historyClick() {
			this.selectRighttItem = this.userSubject.selectRighttItem;
			this.oldChildenRightItem = this.userSubject.oldChildenRightItem;
			// this.openRightMenu(this.selectRighttItem);
			uni.navigateTo({
				url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + this.oldChildenRightItem.repositoryId,
			});
		},
		//获取左边一级菜单
		getLeftList() {
			getLectureCourseList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.leftMenu = res.data.list;
						// this.selectLeftItem = this.leftMenu[0];
						// this.openLeftMenuChilden(this.selectLeftItem);
						if (this.leftMenu.length == 0) {
							this.opacity = 0;
						} else {
							this.opacity = 1;
							this.getRightList();
						}
					} else {
						this.$showToast(res.message || " 查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || " 查询失败，请重试");
				});
		},
		//获取右边一级菜单
		getRightList() {
			this.courseId = this.leftMenu[0].courseId;
			getlectureNotesProfessionList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.rightMenu = res.data.list;
					} else {
						this.$showToast(res.message || " 查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || " 查询失败，请重试");
				});
		},
		//左边二级菜单
		openLeftMenuChilden(item2) {
			this.oldChildenItem = item2;
			this.openRightMenu(item2);
		},
		//左边一级菜单
		openLeftMenu(item) {
			this.params.courseId = item.courseId;
			getlectureNotesProfessionList(this.params)
				.then(res => {
					if (res.result == "1") {
						if (res.data.list.length == 0) {
							this.$showToast("暂无数据");
						}
						item.childen = res.data.list;
						this.rightMenu = res.data.list;
						if (this.selectLeftItem.courseId == item.courseId) {
							this.selectLeftItem = {};
						} else {
							this.selectLeftItem = item;
						}
					} else {
						this.$showToast(res.message || " 查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || " 查询失败，请重试");
				});
		},
		//右边二级菜单
		openRightMenuChilden(item2, index2) {
			this.oldChildenRightItem = item2;
			let userSubject = {
				selectRighttItem: this.selectRighttItem,
				oldChildenRightItem: this.oldChildenRightItem,
				selectLeftItem: this.selectLeftItem,
				oldChildenItem: this.oldChildenItem,
				name: this.selectRighttItem.professionName + "-" + this.oldChildenRightItem.chapterName,
			};
			if (Object.keys(this.selectLeftItem).length == 0) {
				userSubject.selectLeftItem = this.leftMenu[0];
			}
			uni.setStorageSync("userSubject", userSubject);
			if (this.lettureNotes.typeSwitch == 0) {
				if (index2 <= 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买讲义功能",
						cancelColor: "#c6c6c6",
						cancelText: "继续预览",
						// confirmColor: "#0693ff",
						confirmText: "购买讲义",
						success: res => {
							if (res.confirm) {
								uni.setStorageSync("startPage", uni.$u.page());
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=4", // courseType=4 购买讲义
								});
							} else if (res.cancel) {
								uni.navigateTo({
									url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + item2.repositoryId,
								});
							}
						},
					});
				}
				if (index2 > 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买讲义功能",
						cancelColor: "#c6c6c6",
						cancelText: "取消",
						// confirmColor: "#0693ff",
						confirmText: "购买讲义",
						success: res => {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=4",
								});
							}
						},
					});
				}
			} else {
				uni.navigateTo({
					url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + item2.repositoryId,
				});
			}
			// return;
			// if (index2 > 4) {
			// 	console.log("当前章节索引：", index2);
			// 	if (this.lettureNotes.typeSwitch == 0) {
			// 		uni.navigateTo({
			// 			url: "/pages/order/orderPay?courseType=3",
			// 		});
			// 	} else {
			// 		uni.navigateTo({
			// 			url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + item2.repositoryId,
			// 		});
			// 	}
			// } else {
			// 	uni.navigateTo({
			// 		url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + item2.repositoryId,
			// 	});
			// }
		},
		//右边一级菜单
		openRightMenu(item) {
			this.params.courseId = this.selectLeftItem.courseId || this.leftMenu[0].courseId;
			this.params.professionId = item.professionId;
			getChapterList(this.params)
				.then(res => {
					if (res.result == "1") {
						if (res.data.list.length == 0) {
							this.$showToast("暂无章节内容");
							return;
						}
						item.childen = res.data.list;
						if (this.selectRighttItem.professionId == item.professionId) {
							this.selectRighttItem = {};
						} else {
							this.selectRighttItem = item;
							this.oldChildenItem = item;
						}
					} else {
						this.$showToast(res.message || " 查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || " 查询失败，请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height + 10;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	// background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
	.top-body {
		width: calc(100% - 69rpx);
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		.top-body-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			.historical-records {
				min-width: 109rpx;
				max-width: 308rpx;
				height: 40rpx;
				background: #ffffff;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				padding-left: 5rpx;
				padding-right: 5rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 10rpx;
				text {
					ax-width: 308rpx;
					font-weight: bold;
					font-size: 20rpx;
					color: #2168fe;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				image {
					margin-top: 4rpx;
					width: 21.5rpx;
					height: 21.5rpx;
				}
			}
		}
		.unlock {
			width: 152rpx;
			height: 72rpx;
		}
	}
	.top-foot {
		// margin-top: 20rpx;
		width: 100%;
		height: 64rpx;
		background: #c1d6f0;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.top-foot-left {
			width: 280rpx;
			height: 64rpx;
			background: #ffffff;
			border-radius: 0rpx 30rpx 0rpx 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			image {
				width: 28.82rpx;
				height: 26.24rpx;
			}
		}
		.top-foot-right {
			width: calc(100% - 280rpx);
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			image {
				width: 28.82rpx;
				height: 26.24rpx;
			}
		}
	}
}
.main {
	width: 100%;
	position: fixed;
	display: flex;
	justify-content: flex-start;
	z-index: 9;
}
.scroll-L {
	width: 280rpx;
	background: #ffffff;
}
.scroll-R {
	width: calc(100% - 280rpx);
}
.rightMenu {
	width: calc(100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	.rightMenu-title {
		width: calc(100% - 20rpx);
		margin-left: 20rpx;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: flex-start;

		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #8590a7;
			image {
				width: 12rpx;
				height: 24rpx;
			}
		}
	}
	.rightMenu-title-active {
		width: calc(100% - 20rpx);
		margin-left: 20rpx;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #8590a7;
			image {
				width: 12rpx;
				height: 24rpx;
			}
		}
	}
	.rightMenu-box {
		width: calc(100% - 47rpx);
		height: 0;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: height 0.5s ease-in-out;
		.rightMenu-content {
			width: 100%;
			// height: 64rpx;
			height: 76rpx;
			padding-top: 10rpx;
			padding-bottom: 10rpx;
			display: flex;
			// justify-content: space-between;
			justify-content: space-around;
			align-items: center;

			.right-content-l {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 20rpx;
				font-weight: 500;
				font-size: 28rpx;

				.text {
					margin-left: 25rpx;
					// width: 202rpx;
					width: 320rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.sing {
					padding: 5rpx;
					width: 52rpx;
					height: 28rpx;
					background: #dce7ff;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					text {
						margin-left: 0;
						font-weight: 400;
						font-size: 20rpx;
						color: #2168fe;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
			.right-content-r {
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				margin-right: 25rpx;
				width: 40rpx;
				.text-1 {
					font-weight: bold;
					font-size: 28rpx;
					color: #1f2638;
				}
				.text-2 {
					font-weight: bold;
					font-size: 20rpx;
					color: #8590a7;
				}
				.text-3 {
					font-weight: bold;
					font-size: 20rpx;
					color: #8590a7;
				}
			}
		}

		.active {
			width: 100%;
			// height: 64rpx;
			height: 76rpx;
			background: #2168fe;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
			color: white !important;
			.sing {
				padding: 5rpx;
				width: 52rpx;
				height: 28rpx;
				background: white !important;
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				text {
					margin-left: 0;
					font-weight: 400;
					font-size: 20rpx;
					color: #2168fe;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
			.right-content-r {
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				margin-right: 25rpx;
				.text-1 {
					font-weight: bold;
					font-size: 28rpx;
					color: #ffffff !important;
				}
				.text-2 {
					font-weight: bold;
					font-size: 20rpx;
					color: #8590a7;
				}
				.text-3 {
					font-weight: bold;
					font-size: 20rpx;
					color: #8590a7;
				}
			}
		}
	}
}
.leftMenu {
	width: 280rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.leftMenu-title {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		width: 200rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 500;
		font-size: 28rpx;
		color: #8590a7;
	}
	.leftMenu-title-active {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		width: 200rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: bold;
		font-size: 28rpx;
		color: #1f2638;
	}
	.leftMenu-box {
		width: 280rpx;
		height: 0;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: height 0.3s ease; /* 添加过渡效果 */
		.leftMenu-content {
			width: 280rpx;
			height: 68rpx;
			display: flex;
			align-items: center;
			font-weight: 500;
			font-size: 28rpx;
			color: #8590a7;
			border-left: 10rpx #ffffff solid;
			text {
				margin-left: 58rpx;
				width: 202rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
		.active {
			background: rgba(33, 157, 254, 0.1);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			font-weight: bold;
			font-size: 28rpx;
			color: #2168fe;
			border-left: 10rpx #2168fe solid;
		}
	}
}
.scale-in-ver-top {
	-webkit-animation: scale-in-ver-top 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
	animation: scale-in-ver-top 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2025-1-4 15:4:39
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-in-ver-top
 * ----------------------------------------
 */
@-webkit-keyframes scale-in-ver-top {
	0% {
		height: 0rpx;
		opacity: 1;
	}
	100% {
		height: 100%;
		opacity: 1;
	}
}
@keyframes scale-in-ver-top {
	0% {
		height: 0rpx;
		opacity: 1;
	}
	100% {
		height: 100%;
		opacity: 1;
	}
}
</style>
