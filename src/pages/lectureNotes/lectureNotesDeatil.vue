<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="讲义背诵详情"
				@leftClick="goBack()"
				leftIconColor="#212838"
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view
				class="search"
				@click="show = true">
				<view class="search-content">
					<view
						style="display: flex; justify-content: flex-start; align-items: center; gap: 12rpx"
						v-for="(item, index) in chapter"
						:key="index">
						<text>{{ item }}</text>
						<view
							class="line"
							v-if="index < chapter.length - 1"></view>
					</view>
				</view>
				<image
					:src="imagebaseurl + 'static/icon/arrow-down.png'"
					mode=""></image>
			</view>
			<view
				v-if="cardType == 0"
				class="card flip-vertical-right">
				<view class="card-top">
					<view
						class="card-top-box"
						style="gap: 10rpx; margin-left: 30rpx">
						<image
							class="icon-1"
							:src="imagebaseurl + 'static/icon/daotu_icon.png'"
							mode=""></image>
						<text class="card-top-title">{{ chapter[chapter.length - 1] }}</text>
					</view>
					<view
						class="card-top-box"
						style="gap: 42rpx; margin-right: 30rpx">
						<view
							class="radius-1"
							@click="flip(1)">
							<text>{{ cardType == 0 ? "导图记忆" : "查看讲义" }}</text>
						</view>
						<view
							class="radius-2"
							@click="confirmCollection">
							<image
								v-if="list[params.userSubjectIndex].isCollect == 1"
								:src="imagebaseurl + 'static/icon/xx_icon_2.png'"
								mode=""></image>
							<image
								v-if="list[params.userSubjectIndex].isCollect == 0"
								:src="imagebaseurl + 'static/icon/xx_icon.png'"
								mode=""></image>
						</view>
					</view>
				</view>
				<swiper
					class="swiper"
					circular
					:current="params.userSubjectIndex"
					acceleration
					@change="change">
					<swiper-item
						v-for="item in list"
						:key="item.id">
						<view class="swiper-box">
							<view class="title">
								<u-parse :content="item.subjectTitle"></u-parse>
							</view>
							<view class="card-content">
								<scroll-view
									scroll-y="true"
									class="scroll-Y">
									<u-parse :content="item.subjectAnswer"></u-parse>
								</scroll-view>
							</view>
							<view
								class="card-tishi"
								@click="flip(1)">
								<image
									:src="imagebaseurl + 'static/icon/tishi_icon.png'"
									mode=""></image>
								<text>点击翻转查看导图记忆</text>
							</view>
						</view>
					</swiper-item>
				</swiper>
				<view class="card-bottom">
					<view
						:class="switchPageBoolean == false ? 'card-bottom-box-1 active' : 'card-bottom-box-1'"
						@click="switchPage(false)">
						<u-icon
							name="arrow-left"
							:color="switchPageBoolean == false ? 'white' : 'blacke'"></u-icon>
					</view>
					<view class="card-bottom-box-2">
						<view class="pageNume">
							<text>{{ params.userSubjectIndex + 1 }}</text>
							<text>/</text>
							<text>{{ params.userSubjectEndIndex }}</text>
						</view>
					</view>
					<view
						:class="switchPageBoolean == true ? 'card-bottom-box-1 active' : 'card-bottom-box-1'"
						@click="switchPage(true)">
						<u-icon
							name="arrow-right"
							:color="switchPageBoolean == true ? 'white' : 'blacke'"></u-icon>
					</view>
				</view>
			</view>
			<!-- 思维导图 -->
			<view
				v-if="cardType == 1"
				class="card flip-vertical-right'">
				<view class="card-top">
					<view
						class="card-top-box"
						style="gap: 10rpx; margin-left: 30rpx">
						<image
							class="icon-1"
							:src="imagebaseurl + 'static/icon/daotu_icon.png'"
							mode=""></image>
						<text class="card-top-title">{{ chapter[chapter.length - 1] }}</text>
					</view>
					<view
						class="card-top-box"
						style="gap: 42rpx; margin-right: 30rpx">
						<view
							class="radius-1"
							@click="flip(0)">
							<text>{{ cardType == 0 ? "导图记忆" : "查看讲义" }}</text>
						</view>
						<view
							class="radius-2"
							@click="confirmCollection">
							<image
								v-if="list[params.userSubjectIndex].isCollect == 1"
								:src="imagebaseurl + 'static/icon/xx_icon_2.png'"
								mode=""></image>
							<image
								v-if="list[params.userSubjectIndex].isCollect == 0"
								:src="imagebaseurl + 'static/icon/xx_icon.png'"
								mode=""></image>
						</view>
					</view>
				</view>
				<view
					class="card-content"
					style="background: none; height: 962rpx">
					<scroll-view
						scroll-y="true"
						class="scroll-Y"
						style="height: 962rpx">
						<view style="width: 100%; display: flex; align-items: center; justify-content: center">
							<u--image
								height="962rpx"
								:src="list[params.userSubjectIndex].mindMapMemory"
								mode="aspectFit"
								@click="previewImage(list[params.userSubjectIndex].mindMapMemory)">
								<view
									slot="error"
									style="font-size: 24rpx"
									>加载失败</view
								>
							</u--image>
						</view>
					</scroll-view>
				</view>
				<view
					class="card-tishi"
					@click="flip(0)">
					<image
						:src="imagebaseurl + 'static/icon/tishi_icon.png'"
						mode=""></image>
					<text>点击反转查看讲义</text>
				</view>
			</view>
			<!-- 答案 -->

			<view class="bottom">
				<view
					class="bottom-box-1"
					@click="submitReportUserLectureNotes(list[params.userSubjectIndex], 0)">
					<image
						:src="imagebaseurl + 'static/icon/bsjy_icon1.png'"
						mode=""></image>
					<text>未掌握</text>
				</view>
				<view
					class="bottom-box-2"
					@click="submitReportUserLectureNotes(list[params.userSubjectIndex], 1)">
					<image
						:src="imagebaseurl + 'static/icon/bsjy_icon2.png'"
						mode=""></image>
					<text>未完全掌握</text>
				</view>
				<view
					class="bottom-box-3"
					@click="submitReportUserLectureNotes(list[params.userSubjectIndex], 2)">
					<image
						:src="imagebaseurl + 'static/icon/bsjy_icon3.png'"
						mode=""></image>
					<text>已掌握</text>
				</view>
			</view>
		</view>
		<view v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				:defaultIndex="[defaultIndex]"
				keyName="chapterName"
				confirmColor="#2168fe"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getLectureNotesRepositoryPaget, getChapterList, reportUserLectureNotes, addCollection, cancleCollection, getSwitchConfig } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			user: uni.getStorageSync("user"),
			params: {
				pageNo: 1,
				pageSize: 15,
				lectureNotesRepositoryId: 0,
				userSubjectIndex: 0,
			},
			list: [],
			switchPageBoolean: true,
			userSubject: uni.getStorageSync("userSubject"),
			chapter: [],
			show: false,
			columns: [[]],
			defaultIndex: 0,
			flipBoolean: false,
			content: `
								<p>
								(-) 概念
								民事法律事实，是指能够引起民事法律关系产生、
								变更或消灭的客观现象。
								</p>
								<p>
								(二) 特征
								1.民事法律事实是一种客观存在的社会生活中出现的事实，
								而不是当事人主观的内心意思。
								2.民事法律事实必须能够引起一定法律后果。
								3.法律事实能否引起一定的法律后果或引起何种特定的法律后果，最终都取决于法律的规定。
								(三)分类依据:是否与民事主体的意志有关
								1.事件又称自然事实，能够引起是指与人的意志无关，能民事法律关系产生、变更、消灭的客观现象。
								2.行为是指人的有意识的活动。
								行为可以分为以下两种类型:
								</p>
							`,
			lettureNotes: {},
			nexPageBoolean: true,
		};
	},

	onLoad(option) {
		this.params.lectureNotesRepositoryId = Number(option.lectureNotesRepositoryId);
		this.params.globalSchoolId = this.user.globalSchoolId;
		this.params.globalProfessionId = this.user.globalProfessionId;
		this.chapter = this.userSubject.oldChildenRightItem.chapterName.split(" ");
		getSwitchConfig({ orderType: 4 })
			.then(res => {
				if (res.result == "1") {
					this.userConfig = res.data;
					this.lettureNotes = res.data.find(item => item.orderType == 4);
				} else {
					this.$showToast(res.message || "配置异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "配置，请重试");
			});
	},
	mounted() {
		this.getElementHeight();
		this.getLectur();
		this.openPicker();
	},
	methods: {
		goBack() {
			uni.navigateBack({
				delta: 1,
				success: function () {
					// 通知所有监听onBack事件的页面执行相应的操作
					uni.$emit("onBack", { msg: true });
				},
			});
		},
		flip(index) {
			this.cardType = index;
			this.flipBoolean = !this.flipBoolean;
			// setTimeout(() => {
			// 	this.cardType = 0;
			// });
			// deleteCollection()
		},
		confirmCollection() {
			if (this.list[this.params.userSubjectIndex].isCollect == 1) {
				const params = {
					subjectId: this.list[this.params.userSubjectIndex].id,
					collectType: 2,
				};

				cancleCollection(params)
					.then(res => {
						if (res.result == "1") {
							this.$showToast("取消收藏成功");
							this.list[this.params.userSubjectIndex].isCollect = 0;
						} else {
							this.$showToast(res.message || "取消收藏失败，请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "取消收藏失败，请重试");
					});
			}
			if (this.list[this.params.userSubjectIndex].isCollect == 0) {
				//添加收藏
				const params = {
					subjectId: this.list[this.params.userSubjectIndex].id,
					collectType: 2,
					subjectRepositoryId: this.list[this.params.userSubjectIndex].lectureNotesRepositoryId,
					globalSchoolId: this.user.globalSchoolId,
					globalProfessionId: this.user.globalProfessionId,
				};
				addCollection(params)
					.then(res => {
						if (res.result == "1") {
							this.$showToast("收藏成功");
							this.list[this.params.userSubjectIndex].isCollect = 1;
						} else {
							this.$showToast(res.message || "收藏失败，请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "收藏失败，请重试");
					});
			}
		},
		//上报熟练度
		submitReportUserLectureNotes(item, status) {
			//status 0 未掌握 1 未完全掌握 2 已掌握
			reportUserLectureNotes({
				lectureNotesRepositoryId: this.userSubject.oldChildenRightItem.repositoryId,
				subjectId: item.id,
				subjectHold: status,
				globalSchoolId: this.user.globalSchoolId,
				// globalProfessionId: this.user.globalProfessionId
				globalProfessionId: 1,
			})
				.then(res => {
					if (res.result == "1") {
						// this.$showToast("操作成功");
						this.list[this.params.userSubjectIndex].isHold = 1;
						this.toastTopic();
						if (this.params.userSubjectIndex < this.params.userSubjectEndIndex - 1) {
							this.params.userSubjectIndex++;
							this.pageLecture();
						}
					} else {
						this.$showToast(res.message || "操作失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "操作失败，请重试");
				});
		},
		toastTopic() {
			let index = this.list.findIndex(item => item.isHold == 0);
			if (index == -1) {
				this.$showToast("您已完成该章节讲义");
			}
		},
		//获取picke列表数据
		openPicker() {
			getChapterList({
				masterType: this.user.masterType,
				professionId: this.userSubject.selectRighttItem.professionId,
				courseId: this.userSubject.selectLeftItem.courseId,
			})
				.then(res => {
					if (res.result == "1") {
						this.columns[0] = res.data.list;
						this.defaultIndex = this.columns[0].findIndex(item => item.chapterId == this.userSubject.oldChildenRightItem.chapterId);
					} else {
						this.$showToast(res.message || "获取讲义列表失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取讲义列表失败，请重试");
				});
		},
		//picker确认事件
		confirm(e) {
			let index = this.columns[0].findIndex(item => item.chapterId == e.value[0].chapterId);
			if (this.lettureNotes.typeSwitch == 0) {
				if (index <= 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买讲义功能",
						cancelColor: "#c6c6c6",
						cancelText: "继续预览",
						// confirmColor: "#0693ff",
						confirmText: "购买讲义",
						success: res => {
							if (res.confirm) {
								uni.setStorageSync("startPage", uni.$u.page());
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=4",
								});
							} else if (res.cancel) {
								this.switchLectureNotes(e);
							}
						},
					});
				}
				if (index > 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买讲义功能",
						cancelColor: "#c6c6c6",
						cancelText: "取消",
						// confirmColor: "#0693ff",
						confirmText: "购买讲义",
						success: res => {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=4",
								});
							}
						},
					});
				}
			} else {
				this.switchLectureNotes(e);
			}
		},
		//切换讲义章节
		switchLectureNotes(e) {
			this.userSubject.oldChildenRightItem = e.value[0];
			this.userSubject.name = this.userSubject.selectRighttItem.professionName + "-" + e.value[0].chapterName;
			this.chapter = e.value[0].chapterName.split(" ");
			this.params.lectureNotesRepositoryId = e.value[0].repositoryId;
			uni.setStorageSync("userSubject", this.userSubject);
			this.params.pageNo = 1;
			this.params.pageSize = 15;
			this.params.userSubjectIndex = 0;
			this.list = [];
			this.getLectur();
			this.defaultIndex = this.columns[0].findIndex(item => item.chapterId === this.userSubject.oldChildenRightItem.chapterId);
			this.cardType = 0;
			this.show = false;
		},
		//分页
		switchPage(boolean) {
			console.log("当前题目页数：", this.params.userSubjectIndex);
			if (boolean) {
				if (this.params.userSubjectIndex < this.params.userSubjectEndIndex) {
					this.params.userSubjectIndex++;
					this.pageLecture();
				}
			} else {
				if (this.params.userSubjectIndex > 0) {
					this.params.userSubjectIndex--;
				}
			}
			this.switchPageBoolean = boolean;
		},
		//用户主动滑动分页
		change(e) {
			this.params.userSubjectIndex = e.detail.current;
			this.pageLecture();
		},
		//获取讲义分页数据
		getLectur() {
			getLectureNotesRepositoryPaget(this.params)
				.then(res => {
					if (res.result == "1") {
						if (res.data.list.length == 0) {
							this.nexPageBoolean = false;
						}
						this.list = [...this.list, ...res.data.list];

						this.params.userSubjectEndIndex = this.list[0].maxSubjectIndex;
						// this.params.userSubjectIndex = res.data.list[0].subjectIndex - 1;
						this.params.userSubjectIndex = this.userSubject.oldChildenRightItem.userSubjectIndex;
						// 当用户全部标记过讲义时，处理页面显示的当前讲义位置数会加1,所以这里会减1保存页面显示一致
						if (this.params.userSubjectIndex == this.list[0].maxSubjectIndex) {
							this.params.userSubjectIndex = this.list[0].maxSubjectIndex - 1;
						}
					} else {
						this.$showToast(res.message || "获取讲义内容失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取讲义内容失败，请重试");
				});
		},
		//分页调用，这里不会重置当前题目的位置
		pageLecture() {
			if (this.params.userSubjectEndIndex - this.params.userSubjectIndex > 3) {
				if (this.nexPageBoolean) {
					this.params.pageNo++;
					getLectureNotesRepositoryPaget(this.params)
						.then(res => {
							if (res.result == "1") {
								if (res.data.list.length == 0) {
									this.nexPageBoolean = false;
								}
								this.list = [...this.list, ...res.data.list];
							} else {
								this.$showToast(res.message || "获取讲义内容失败，请重试");
							}
						})
						.catch(err => {
							this.$showToast(err.message || "获取讲义内容失败，请重试");
						});
				}
			}
		},
		previewImage(url) {
			if (url) {
				uni.previewImage({
					urls: [url],
				});
			}
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	padding-bottom: 100rpx;
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.bottom {
	margin-top: 40rpx;
	width: calc(100% - 90rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	.bottom-box-1 {
		width: 192rpx;
		height: 80rpx;
		background: #f8f9fb;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(129, 147, 174, 0.14);
		border-radius: 60rpx 20rpx 20rpx 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 8rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #f65453;
		}
		image {
			width: 48rpx;
			height: 48rpx;
		}
	}
	.bottom-box-2 {
		width: 240rpx;
		height: 80rpx;
		background: #f8f9fb;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(129, 147, 174, 0.14);
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 8rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #1f2638;
		}
		image {
			width: 48rpx;
			height: 48rpx;
		}
	}
	.bottom-box-3 {
		width: 192rpx;
		height: 80rpx;
		background: #f8f9fb;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(129, 147, 174, 0.14);
		border-radius: 20rpx 60rpx 60rpx 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 8rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #4e86fc;
		}
		image {
			width: 48rpx;
			height: 48rpx;
		}
	}
}
.swiper {
	width: calc(100%);
	height: 960rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.swiper-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.card {
	margin-top: 26rpx;
	width: calc(100% - 80rpx);
	height: 1180rpx;
	background: #ffffff;
	border-radius: 40rpx 40rpx 40rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.card-top {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(44deg, #c6d8f1 0%, #dae6f6 100%);
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.card-top-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.icon-1 {
				width: 39.35rpx;
				height: 32rpx;
			}
			.card-top-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 40rpx;
				color: #1f4085;
				width: 282rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.radius-1 {
				width: 140rpx;
				height: 60rpx;
				border-radius: 30rpx 30rpx 30rpx 30rpx;
				background-color: #ffffff;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #1f4085;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.radius-2 {
				width: 60rpx;
				height: 60rpx;
				background: #ffffff;
				box-shadow: inset 2rpx 2rpx 8rpx 2rpx rgba(145, 189, 255, 0.1);
				border-radius: 30rpx 30rpx 30rpx 30rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				image {
					width: 35.94rpx;
					height: 34.4rpx;
				}
			}
		}
	}

	.title {
		margin-top: 24rpx;
		width: calc(100% - 50rpx);
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 32rpx;
		color: #1f2638;
	}
	.card-content {
		margin-top: 20rpx;
		width: 630rpx;
		height: 820rpx;
		background: rgba(240, 241, 245, 0.5);
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		.scroll-Y {
			height: 820rpx;
		}
	}
	.card-tishi {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 20rpx;
		color: #75ce98;
		image {
			width: 15.71rpx;
			height: 20rpx;
		}
	}
	.card-bottom {
		margin-top: 36rpx;
		width: calc(100% - 80rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.card-bottom-box-1 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100rpx;
			height: 60rpx;
			background: #f7f7f9;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
			border: 1rpx solid #dde1e9;
		}
		.active {
			background: #2168fe;
		}
		.card-bottom-box-2 {
			width: 200rpx;
			height: 60rpx;
			background: #f7f7f9;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			.pageNume {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 5rpx;
				text:nth-child(1) {
					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 24rpx;
					color: #2168fe;
				}
				text {
					font-family: DINPro, DINPro;
					font-weight: bold;
					font-size: 24rpx;
					color: #a8aebe;
				}
			}
		}
	}
}
.line {
	width: 2rpx;
	height: 28rpx;
	background: linear-gradient(180deg, rgba(123, 126, 154, 0) 0%, rgba(123, 126, 154, 0.5) 52%, rgba(123, 126, 154, 0) 100%);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
}
.search {
	width: calc(100% - 80rpx);
	height: 76rpx;
	background: rgba(31, 64, 133, 0.1);
	border-radius: 38rpx 38rpx 38rpx 38rpx;
	border: 2rpx solid #1f4085;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.search-content {
		margin-left: 20rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 12rpx;
		text {
			font-weight: 400;
			font-size: 24rpx;
			color: #212838;
		}
	}
	image {
		margin-right: 20rpx;
		width: 24rpx;
		height: 24rpx;
	}
}
.card-answer {
	margin-top: 26rpx;
	width: calc(100% - 80rpx);
	height: 1180rpx;
	background: #ffffff;
	border-radius: 40rpx 40rpx 40rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.top-icon {
		margin-top: 50rpx;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;
		left: -10rpx;
		.answer_correct-icon {
			position: absolute;
			top: 12rpx;
			left: 19rpx;
			width: 40rpx;
			height: 40rpx;
		}
		.answer-icon {
			width: 230rpx;
			height: 81.92rpx;
		}
	}
	.answer-content {
		margin-top: 20rpx;
		width: calc(100% - 60rpx);
		height: 828rpx;
		// border: 1rpx solid;
	}
}
</style>
