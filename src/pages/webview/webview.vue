<template>
	<view>
		<web-view :src="src"></web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			src: "",
			//0我的课程
			type: 0,
			seconds: 0, // 计时器的秒数
			isRunning: false, // 计时器是否正在运行
			timer: null, // 定时器ID
		};
	},
	onLoad(option) {
		this.src = option.url;
		this.type = Number(option.type);
		if (this.type == 1) {
			this.src = uni.getStorageSync("webviewUrl");
		}
	},

	beforeDestroy() {
		clearInterval(this.timer); // 组件销毁时清除定时器
	},
	methods: {
		startTimer() {
			if (!this.isRunning) {
				this.isRunning = true;
				this.timer = setInterval(() => {
					this.seconds++;
				}, 1000);
			}
		},
	},
};
</script>

<style></style>
