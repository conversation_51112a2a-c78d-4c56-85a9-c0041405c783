<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="订单支付"
				leftIconColor="#212838"
				autoBack
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="time-box">
				<u-count-down
					:time="30 * 60 * 60 * 1000"
					format="HH:mm:ss"></u-count-down>
				<view class="time-text">
					<image
						:src="imagebaseurl + 'static/icon/time-icon.png'"
						mode=""></image>
					<text>支付剩余时间</text>
				</view>
			</view>
			<view
				class="shop-box"
				v-for="(item, index) in orderList"
				:key="index">
				<u-radio-group
					v-model="item.orderExtCourseType"
					:value="item.orderExtCourseType">
					<u-radio
						shape="circle"
						label=" "
						:name="item.orderExtCourseType">
					</u-radio>
				</u-radio-group>
				<view class="shop-content">
					<view class="shop-top">
						<image
							src="../../static/icon/money.png"
							mode=""></image>
						<text>￥{{ item.price }}</text>
					</view>
					<view class="shop-bottom">
						<image
							class="shop-image"
							:src="item.orderPic"
							mode=""></image>
						<view class="shop-message">
							<view class="shop-message-top">
								<text>{{ item.orderTitle }}</text>
								<text style="font-size: 26rpx">￥{{ item.price }}</text>
							</view>
							<text class="text">{{ item.orderDescription }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 			<scroll-view
				scroll-y="true"
				class="scroll-Y">
				<view class="swiper-box">
					<view
						class="shop-box"
						style="margin-top: 88rpx"
						v-for="(item, index) in orderList"
						:key="index">
						<u-radio-group
							v-model="item.orderExtCourseType"
							:value="item.orderExtCourseType">
							<u-radio
								shape="circle"
								label=" "
								:name="item.orderExtCourseType">
							</u-radio>
						</u-radio-group>
						<view class="shop-content">
							<view class="shop-top">
								<image
									src="../../static/icon/money.png"
									mode=""></image>
								<text>￥{{ item.price }}</text>
							</view>
							<view class="shop-bottom">
								<image
									class="shop-image"
									:src="item.orderPic"
									mode=""></image>
								<view class="shop-message">
									<view class="shop-message-top">
										<text>{{ item.orderTitle }}</text>
										<text style="font-size: 26rpx">￥{{ item.price }}</text>
									</view>
									<text class="text">{{ item.orderDescription }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view> -->
			<view class="pay-box">
				<text class="text">支付方式</text>
				<view
					class="pay-menthod"
					@click="payMethods(2)">
					<view class="pay-menthod-left">
						<image
							:src="imagebaseurl + 'static/icon/pay-weixn.png'"
							mode=""></image>
						<text>微信支付</text>
					</view>
					<view style="width: 50rpx; margin-right: 30rpx">
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 2"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 3"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
					</view>
				</view>
				<view
					class="pay-menthod"
					@click="payMethods(3)">
					<view class="pay-menthod-left">
						<image
							:src="imagebaseurl + 'static/icon/youzan_icon.png'"
							mode=""></image>
						<text>有赞支付</text>
					</view>
					<view style="width: 50rpx; margin-right: 30rpx">
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 3"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 2"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
					</view>
				</view>
			</view>
		</view>
		<view class="foot">
			<view class="pay-foot">
				<text class="text-price">￥{{ countPirce }}</text>
				<view
					class="pay-btn"
					@click="submit()">
					<text>立即支付</text>
				</view>
			</view>
		</view>
		<u-popup
			:show="show"
			@close="close"
			:safeAreaInsetBottom="true">
			<view class="pay-card">
				<view class="pay-top">
					<text>有赞支付</text>
					<image
						:src="imagebaseurl + 'static/icon/close_icon_1.png'"
						mode=""
						@click="show = false"></image>
				</view>
				<u-line
					length="100%"
					margin="20rpx"></u-line>
				<view class="price">
					<text>￥</text>
					<text>0</text>
				</view>
				<view class="user">
					<text>有赞账号：</text>
					<u--input
						v-model="youzanOrderInfo.youzanAccountNo"
						placeholder="请输入账号"
						border="surround"></u--input>
				</view>
				<view class="user">
					<text>订单编号：</text>
					<u--input
						v-model="youzanOrderInfo.youzanOrderNo"
						placeholder="请输入订单编号"
						border="surround"></u--input>
				</view>

				<view class="pay-foot">
					<text class="text-price">￥0</text>
					<view
						class="pay-btn"
						@click="reportYouzan">
						<text>提交审核</text>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getOrderConfig, reportYouzanData, createOrder, continuePay } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			user: uni.getStorageSync("user"),
			params: {
				pageNo: 1,
				pageSize: 10,
			},
			checkboxValue: [],
			user: uni.getStorageSync("user"),
			orderList: [],
			//0 初试系统课程 1初试冲刺课程 3复试课程
			courseType: 0,
			show: false,
			countPirce: 0,
			youzanOrderInfo: {
				youzanAccountNo: "",
				youzanOrderNo: "",
			},
			sing: "",
			wexinSign: {},
			orderNo: "",
			expireTime: "",
			orderType: -1,
			orderExt: "",
			payType: 2,
		};
	},
	onLoad(option) {
		this.courseType = option.courseType;

		this.getOrder();
	},
	mounted() {
		this.getElementHeight();
	},

	methods: {
		submit() {
			let that = this;
			if (this.payType == 2) {
				continuePay({
					orderNo: this.orderNo,
					payType: this.payType,
				}).then(res => {
					if (res.result == "1") {
						let wexinSign = JSON.parse(res.data.sign);
						uni.requestPayment({
							provider: "wxpay",
							appid: wexinSign.appid, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
							nonceStr: wexinSign.nonceStr, // 随机字符串
							package: wexinSign.packageValue,
							timeStamp: wexinSign.timeStamp, // 时间戳（单位：秒）
							paySign: wexinSign.paySign, // 签名，这里用的 MD5/RSA 签名
							signType: wexinSign.signType,
							success(res) {
								console.log("支付成功:" + JSON.stringify(res));
								that.$showToast("支付成功");
								uni.reLaunch({
									url: uni.getStorageSync("startPage"),
								});
							},
							fail(e) {
								console.log("支付失败:" + JSON.stringify(e));
								that.$showToast("支付失败");
							},
						});
					}else{
						this.$showToast(res.message || "支付失败，请重试");
					}
				}).catch(err => {
					this.$showToast("支付失败，请重试");
				});
			}else{
				this.show = true;
			}
		},
		createOrderInfo() {
			if (this.courseType == 4) {
				this.orderType = 4;
			} else {
				this.orderType = 6;
			}
			let params = {
				orderType: this.orderType,
				masterType: this.user.masterType,
				buyerName: this.user.nickName,
				buyerPhone: this.user.phone,
				orderExt: this.orderExt,
			};
			createOrder(params)
				.then(res => {
					if (res.result == "1") {
						this.orderNo = res.data.orderNo;
						this.expireTime = res.data.expireTime;
					} else {
						this.$showToast(res.message || "创建订单失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "创建订单失败");
				});
		},

		reportYouzan() {
			if (this.youzanOrderInfo.youzanAccountNo == "") {
				this.$showToast("请输入有赞账号");
				return;
			}
			if (this.youzanOrderInfo.youzanOrderNo == "") {
				this.$showToast("请输入订单编号");
				return;
			}

			reportYouzanData({
				orderNo: this.orderNo,
				youzanOrderInfo: this.youzanOrderInfo,
			})
				.then(res => {
					if (res.result == "1") {
						this.$showToast("提交成功,等待审核");
						this.show = false;
						this.youzanOrderInfo.youzanAccountNo = "";
						this.youzanOrderInfo.youzanOrderNo = "";
						uni.navigateBack({ delta: 1 });
					} else {
						this.$showToast(res.message || "提交失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "提交失败请重试");
				});
		},
		payMethods(index) {
			this.payType = index;
		},
		close() {
			this.show = false;
		},
		extractMerchantId(prepayId) {
			const wxIndex = prepayId.indexOf("wx");
			if (wxIndex === -1) {
				return null;
			}

			let merchantId = "";
			for (let i = wxIndex + 2; i < prepayId.length; i++) {
				const char = prepayId[i];
				if (/[a-zA-Z0-9]/.test(char)) {
					merchantId += char;
				} else {
					break;
				}
			}
			return merchantId;
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		getOrder() {
			// 1学习包初试系统课程 masterType 1学硕 初试系统+初试冲刺都解锁
			// 2学习包初试冲刺课程 masterType 1学硕
			// 3学习包复试课程 masterType 1学硕
			// 4.学习包法律综合 masterType 2
			// 5.学习包法律基础 masterType 2
			let orderExtCourseTypeList = "";
			if (this.courseType == 1) {
				orderExtCourseTypeList = "1,2";
			} else {
				orderExtCourseTypeList = this.courseType;
			}
			this.orderExt = `{"orderExtCourseType":${this.courseType + ""}}`;
			getOrderConfig({ orderExtCourseTypeList: orderExtCourseTypeList }).then(res => {
				console.table(res.data);
				if (res.result == "1") {
					this.orderList = res.data;
					this.orderList.map(item => {
						this.countPirce += item.price;
					});
					this.createOrderInfo();
				} else {
					this.$showToast(res.message || "查询失败请重试");
				}
			});
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-count-down__text {
	font-family: DINPro, DINPro;
	font-weight: bold;
	font-size: 60rpx !important;
	color: #2168fe !important;
}
.pay-card {
	width: calc(100%);
	// height: 604rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.pay-top {
		width: calc(100% - 50rpx);
		height: 80rpx;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 256rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #1f2638;
		}
		image {
			width: 32.82rpx;
			height: 32.82rpx;
		}
	}
	.price {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: baseline;
		text:nth-child(1) {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #2168fe;
		}
		text:nth-child(2) {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #2168fe;
		}
	}
	.user {
		margin-top: 56rpx;
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 30rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 30rpx;
			color: #1f2638;
		}
	}
	.pay-menthod {
		margin-top: 58rpx;
		width: calc(100% - 50rpx);
		height: 100rpx;
		background: #f6f7fb;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.pay-menthod-left {
			margin-left: 30rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 16rpx;
			image {
				width: 50rpx;
				height: 50rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #1f2638;
			}
		}
	}
	.pay-foot {
		margin-top: 42rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #edeef2;
		.text-price {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #1f2638;
		}
		.pay-btn {
			width: calc(100% - 300rpx);
			height: 98rpx;
			background: #2168fe;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 36rpx;
				color: #ffffff;
			}
		}
	}
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	padding-bottom: 100rpx;
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.scroll-Y {
	width: 100%;
	height: 659rpx;
}
.swiper-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
	.time-box {
		margin-top: 40rpx;
		margin-bottom: 48rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.time-text {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			gap: 10rpx;
			image {
				width: 23.43rpx;
				height: 23.43rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #6582b4;
			}
		}
	}
	.shop-box {
		margin-top: 24rpx;
		width: 94%;
		display: flex;
		justify-content: center;
		align-items: center;
		.shop-content {
			width: calc(100% - 50rpx);
			height: 220rpx;
			background: #ffffff;
			border-radius: 18rpx 18rpx 18rpx 18rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.shop-top {
				margin-top: 10rpx;
				width: calc(100% - 50rpx);
				display: flex;
				align-items: center;
				image {
					width: 40rpx;
					height: 40rpx;
				}
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 42rpx;
					color: #1f4085;
					line-height: 42rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
			.shop-bottom {
				margin-top: 18rpx;
				width: calc(100% - 50rpx);
				display: flex;
				justify-content: flex-start;
				align-items: flex-start;
				gap: 10rpx;
				.shop-image {
					width: 128rpx;
					height: 128rpx;
					background: #ffffff;
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					border: 2rpx solid #ebecf0;
				}
				.shop-message {
					margin-top: 10rpx;
					width: calc(100% - 160rpx);
					display: flex;
					flex-direction: column;

					.shop-message-top {
						display: flex;
						justify-content: space-between;
						align-items: center;
						text {
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 30rpx;
							color: #1f2638;
						}
					}
					.text {
						margin-top: 10rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #8590a7;

						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}
	.pay-box {
		margin-top: 40rpx;
		width: calc(100% - 50rpx);
		height: 349rpx;
		background: #ffffff;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.text {
			margin-top: 20rpx;
			width: calc(100% - 50rpx);
			text-align: left;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #1f2638;
		}
		.pay-menthod {
			margin-top: 26rpx;
			width: calc(100% - 50rpx);
			height: 100rpx;
			background: #f6f7fb;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.pay-menthod-left {
				margin-left: 30rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 16rpx;
				image {
					width: 50rpx;
					height: 50rpx;
				}
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 28rpx;
					color: #1f2638;
				}
			}
		}
	}
}
.foot {
	width: 100%;
	position: fixed;
	bottom: 0;
	.pay-foot {
		margin-top: 42rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #edeef2;
		.text-price {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #1f2638;
		}
		.pay-btn {
			width: calc(100% - 300rpx);
			height: 98rpx;
			background: #2168fe;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 36rpx;
				color: #ffffff;
			}
		}
	}
}
</style>
