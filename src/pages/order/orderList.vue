<template>
	<view class="container">
		<view class="top">
			<u-navbar
				title=" "
				leftText="我的订单"
				:autoBack="true"
				placeholder></u-navbar>
			<view class="tabs">
				<view
					class="tabs-box"
					v-for="(item, index) in tabs"
					:key="index"
					@click="selectTab(item)">
					<text :class="selectedTab.id == item.id ? 'tabs-title active' : 'tabs-title'">{{ item.name }}</text>
					<view :class="selectedTab.id == item.id ? 'line-active' : 'line'"></view>
				</view>
			</view>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px'">
			<view
				class="order"
				v-for="(item, index) in orderList"
				:key="index">
				<view class="order-top">
					<view class="order-top-left">
						<text>{{ item.orderName }}</text>
						<image
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
					<text
						class="sing"
						v-if="item.orderStatus == 0"
						>待付款</text
					>
					<text
						class="sing"
						v-if="item.orderStatus == 2"
						>已取消</text
					>

					<image
						v-if="item.orderStatus == 1"
						:src="imagebaseurl + 'static/icon/unlock_blue.png'"
						mode=""
						style="width: 128rpx; height: 60rpx"></image>
				</view>
				<u-line
					length="100%"
					margin="10rpx 20rpx 20rpx 20rpx"></u-line>
				<view class="shop">
					<image
						class="shop-image"
						:src="item.campImgUrl"
						mode=""></image>
					<view class="shop-message">
						<view class="shop-message-top">
							<text>全包前</text>
							<text style="font-size: 26rpx">￥{{ item.orderPrice }}</text>
						</view>
						<text class="text">包含系统包和冲刺包业务课一和业务课二的讲义、真题、考前模拟四套卷以及赠送的...</text>
					</view>
				</view>
				<text class="shop-price">商品总价：{{ item.orderPrice }}</text>
				<text class="shop-time">课程有效期：{{ item.campStartTime }}-{{ item.campEndTime }}</text>
				<u-line
					length="100%"
					margin="20rpx"></u-line>
				<view
					class="order-foot"
					v-if="item.orderStatus == 0">
					<view class="order-foot-left">
						<text>应付款</text>
						<text>￥{{ item.orderPrice }}</text>
					</view>
					<view class="order-foot-right">
						<view class="btn-1">
							<text @click="openPicker(item, 2)">取消订单</text>
						</view>
						<view
							class="btn-2"
							@click="openPicker(item, 1)">
							<text>立即支付</text>
						</view>
					</view>
				</view>
				<view
					class="order-pay"
					v-if="item.orderStatus == 1">
					<view class="order-pay-left">
						<image
							:src="imagebaseurl + 'static/icon/time-icon.png'"
							mode=""></image>
						<text>2024年5月22日</text>
					</view>
					<view class="order-pay-right">
						<text>已付款</text>
					</view>
				</view>
			</view>
			<u-loadmore
				:status="status"
				:loading-text="loadingText"
				:loadmore-text="loadmoreText"
				:nomore-text="nomoreText" />
			<u-empty
				mode="order"
				text="暂无订单"
				:icon="require('../../static/icon/order-empty.png')"
				marginTop="350rpx"
				v-if="orderList.length == 0"></u-empty>
		</view>
		<u-popup
			:show="show_1"
			@close="close"
			:safeAreaInsetBottom="true">
			<view class="pay-card">
				<view class="pay-top">
					<text>立即支付</text>
					<image
						:src="imagebaseurl + 'static/icon/close_icon_1.png'"
						mode=""
						@click="show_1 = false"></image>
				</view>
				<u-line
					length="100%"
					margin="20rpx"></u-line>
				<view class="price">
					<text>￥</text>
					<text>{{ selectOrderItem.orderPrice }}</text>
				</view>
				<view class="user">
					<text>账号：</text>
					<text>123******02</text>
				</view>
				<view
					class="pay-menthod"
					@click="payMethods(2)">
					<view class="pay-menthod-left">
						<image
							:src="imagebaseurl + 'static/icon/pay-weixn.png'"
							mode=""></image>
						<text>微信支付</text>
					</view>
					<view style="width: 50rpx; margin-right: 30rpx">
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 2"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 3"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
					</view>
				</view>
				<view
					class="pay-menthod"
					@click="payMethods(3)">
					<view class="pay-menthod-left">
						<image
							:src="imagebaseurl + 'static/icon/youzan_icon.png'"
							mode=""></image>
						<text>有赞支付</text>
					</view>
					<view style="width: 50rpx; margin-right: 30rpx">
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 3"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							style="width: 28rpx; height: 28rpx"
							v-if="payType == 2"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
					</view>
				</view>
				<view class="pay-foot">
					<text class="text-price">￥{{ selectOrderItem.orderPrice }}</text>
					<view
						class="pay-btn"
						@click="submitPay()">
						<text>立即支付</text>
					</view>
				</view>
			</view>
		</u-popup>
		<u-popup
			:show="cancelShow"
			@close="close"
			mode="center"
			:safeAreaInsetBottom="fasle"
			:safeAreaInsetTop="false">
			<view class="card">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/cancel_order.png'"
						mode=""></image>
				</view>
				<text class="title">取消订单</text>
				<view class="shop">
					<view class="shop-image"></view>
					<view class="shop-message">
						<view class="shop-message-top">
							<text>全包前</text>
							<text style="font-size: 26rpx">￥{{ selectOrderItem.orderPrice }}</text>
						</view>
						<text class="text">包含系统包和冲刺包业务课一和业务课二的讲义、真题、考前模拟四套卷以及赠送的...</text>
					</view>
				</view>
				<view class="shop-price">
					<text>商品总价：{{ selectOrderItem.orderPrice }}</text>
					<view class="text">
						<text>实付款</text>
						<text>￥{{ selectOrderItem.orderPrice }}</text>
					</view>
				</view>
				<view class="foot">
					<view
						class="btn btn-cancle"
						@click="cancel">
						<text>取消</text>
					</view>
					<view
						class="btn btn-confirm"
						@click="confirm">
						<text>确定</text>
					</view>
				</view>
				<view class="colse">
					<image
						:src="imagebaseurl + 'static/icon/close_icon.png'"
						mode=""
						@click="cancelShow = false"></image>
				</view>
			</view>
		</u-popup>
		<u-popup
			:show="show_2"
			@close="close"
			:safeAreaInsetBottom="true">
			<view class="pay-card">
				<view class="pay-top">
					<text>有赞支付</text>
					<image
						:src="imagebaseurl + 'static/icon/close_icon_1.png'"
						mode=""
						@click="show_2 = false"></image>
				</view>
				<u-line
					length="100%"
					margin="20rpx"></u-line>
				<view class="price">
					<text>￥</text>
					<text>0</text>
				</view>
				<view class="user">
					<text>有赞账号：</text>
					<u--input
						placeholder="请输入账号"
						border="surround"></u--input>
				</view>
				<view class="user">
					<text>订单编号：</text>
					<u--input
						placeholder="请输入订单编号"
						border="surround"></u--input>
				</view>

				<view class="pay-foot">
					<text class="text-price">￥0</text>
					<view class="pay-btn">
						<text>提交审核</text>
					</view>
				</view>
			</view>
		</u-popup>
		<message-toast :show="toastShow"></message-toast>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getOrderList, cancelOrder, reportYouzanData, continuePay } from "../../api/api.js";
import MessageToast from "../../component/comment/MessageToast.vue";
export default {
	components: {
		MessageToast,
	},
	data() {
		return {
			imagebaseurl: imagebaseurl,
			topHeight: 0,
			tabs: [
				{
					id: 1,
					name: "全部",
					status: -1,
				},
				{
					id: 2,
					name: "待付款",
					status: 0,
				},
				{
					id: 3,
					name: "已购买",
					status: 1,
				},
			],
			selectedTab: { id: 1 },
			show_1: false,
			show_2: false,
			cancelShow: false,
			payType: 2,
			toastShow: false,
			params: {
				pageNo: 1,
				pageSize: 10,
				// orderType: 4,
				orderTypeList: "4,6,7,8",
			},
			orderList: [],
			selectOrderItem: {},
			youzanOrderInfo: {
				youzanAccountNo: "",
				youzanOrderNo: "",
			},
			wexinSign: {},
			status: "loadmore",
			loadingText: "努力加载中",
			loadmoreText: "轻轻上拉",
			nomoreText: "实在没有了",
		};
	},
	created() {
		this.getOrder();
	},
	mounted() {
		this.getElementHeight();
	},
	onPullDownRefresh() {
		this.params = {
			pageNo: 1,
			pageSize: 10,
			// orderType: 4,
			orderTypeList: "4,6,7,8",
		};
		this.orderList = [];
		this.getOrder();
		uni.stopPullDownRefresh();
	},
	onReachBottom() {
		this.params.pageNo++;
		this.getOrder();
	},
	methods: {
		//继续支付
		submitPay() {
			let that = this;
			continuePay({
				orderNo: this.selectOrderItem.orderNo,
				payType: this.payType,
			})
				.then(res => {
					if (res.result == "1") {
						this.wexinSign = JSON.parse(res.data.sign);
						this.expireTime = res.data.expireTime;
						uni.requestPayment({
							provider: "wxpay",
							appid: this.wexinSign.appid, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
							nonceStr: this.wexinSign.nonceStr, // 随机字符串
							package: this.wexinSign.packageValue,
							timeStamp: this.wexinSign.timeStamp, // 时间戳（单位：秒）
							paySign: this.wexinSign.paySign, // 签名，这里用的 MD5/RSA 签名
							signType: this.wexinSign.signType,
							success(res) {
								console.log("支付成功:" + JSON.stringify(res));
								that.$showToast("支付成功");
								that.show_1 = false;
								this.orderList = [];
								that.getOrder();
							},
							fail(e) {
								console.log("支付失败:" + JSON.stringify(e));
								that.$showToast("支付失败");
								that.show_1 = false;
							},
						});
					} else {
						this.$showToast(res.message || "订单异常");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "订单异常");
				});
		},
		reportYouzan() {
			if (this.youzanOrderInfo.youzanAccountNo == "") {
				this.$showToast("请输入有赞账号");
				return;
			}
			if (this.youzanOrderInfo.youzanOrderNo == "") {
				this.$showToast("请输入订单编号");
				return;
			}
			reportYouzanData({
				orderNo: this.selectOrderItem.orderNo,
				youzanOrderInfo: this.youzanOrderInfo,
			})
				.then(res => {
					if (res.result == "1") {
						this.$showToast("提交成功,等待审核");
						this.show = false;
						this.youzanOrderInfo.youzanAccountNo = "";
						this.youzanOrderInfo.youzanOrderNo = "";
						uni.navigateBack({ delta: 1 });
					} else {
						this.$showToast(res.message || "提交失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "提交失败请重试");
				});
		},
		payMethods(index) {
			this.payType = index;
			if (this.payType == 3) {
				this.show_1 = false;
				this.show_2 = true;
			}
		},
		getOrder() {
			this.status = "loading";
			getOrderList(this.params)
				.then(res => {
					console.log(res);
					if (res.result == "1") {
						this.orderList = [...this.orderList, ...res.data.list];
						this.status = "nomore";
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		openPicker(item, index) {
			this.selectOrderItem = item;
			if (index == 1) {
				this.show_1 = true;
			}
			if (index == 2) {
				this.cancelShow = true;
			}
		},
		close() {
			this.show = false;
			// console.log('close');
		},
		cancel() {
			this.cancelShow = false;
		},
		confirm() {
			this.cancelShow = false;
			cancelOrder({ orderNo: this.selectOrderItem.orderNo }).then(res => {
				if (res.result == "1") {
					this.toastShow = true;
					this.orderList = [];
					this.params.pageNo = 1;
					this.params.pageSize = 10;
					this.getOrder();
				}
			});
		},
		selectTab(item) {
			this.selectedTab = item;
			this.orderList = [];
			this.params.pageNo = 1;
			this.params.pageSize = 10;
			if (item.status == -1) {
				this.params = {
					pageNo: 1,
					pageSize: 10,
					orderType: 4,
				};
			} else {
				this.params.orderStatus = item.status;
			}
			this.getOrder();
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-icon__icon {
	font-weight: bold !important;
	font-size: 36rpx !important;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.top {
	width: 100%;
	position: fixed;
	top: 0;
	z-index: 999;
}
.tabs {
	padding-left: 49rpx;
	width: 100%;
	background-color: white;
	display: flex;
	justify-content: flex-start;
	gap: 88rpx;
	.tabs-box {
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 22rpx;
		.tabs-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #646d80;
		}
		.active {
			color: #2168fe;
		}
		.line {
			width: 94rpx;
			height: 4rpx;
			background: white;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
		.line-active {
			width: 94rpx;
			height: 4rpx;
			background: #2168fe;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
	}
}

.main {
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 200rpx;
}
.pay-card {
	width: calc(100%);
	// height: 604rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.pay-top {
		width: calc(100% - 50rpx);
		height: 80rpx;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 256rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #1f2638;
		}
		image {
			width: 32.82rpx;
			height: 32.82rpx;
		}
	}
	.price {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: baseline;
		text:nth-child(1) {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #2168fe;
		}
		text:nth-child(2) {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #2168fe;
		}
	}
	.user {
		margin-top: 56rpx;
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: flex-start;
		gap: 30rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 30rpx;
			color: #1f2638;
		}
	}
	.pay-menthod {
		margin-top: 58rpx;
		width: calc(100% - 50rpx);
		height: 100rpx;
		background: #f6f7fb;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.pay-menthod-left {
			margin-left: 30rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 16rpx;
			image {
				width: 50rpx;
				height: 50rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #1f2638;
			}
		}
	}
	.pay-foot {
		margin-top: 42rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #edeef2;
		.text-price {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #1f2638;
		}
		.pay-btn {
			width: calc(100% - 300rpx);
			height: 98rpx;
			background: #2168fe;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 36rpx;
				color: #ffffff;
			}
		}
	}
}
.order {
	margin-top: 20rpx;
	width: calc(100% - 60rpx);
	padding-bottom: 20rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.order-top {
		margin-top: 10rpx;
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.order-top-left {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 20rpx;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #646d80;
			}
			image {
				width: 10rpx;
				height: 16rpx;
			}
		}
		.sing {
			width: 120rpx;
			height: 60rpx;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #ff1515;
		}
	}
	.shop {
		margin-top: 10rpx;
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
		gap: 10rpx;
		.shop-image {
			width: 128rpx;
			height: 128rpx;
			background: #ffffff;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #ebecf0;
		}
		.shop-message {
			margin-top: 10rpx;
			width: calc(100% - 160rpx);
			display: flex;
			flex-direction: column;

			.shop-message-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #1f2638;
				}
			}
			.text {
				margin-top: 10rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #8590a7;

				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.shop-price {
		margin-top: 20rpx;
		width: calc(100% - 50rpx);
		text-align: left;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #8590a7;
	}
	.shop-time {
		margin-top: 10rpx;
		width: calc(100% - 50rpx);
		text-align: left;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #1f2638;
	}
	.order-pay {
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.order-pay-left {
			display: flex;
			justify-content: flex-start;
			align-items: baseline;
			gap: 10rpx;
			image {
				width: 23.43rpx;
				height: 23.34rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 22rpx;
				color: #8590a7;
			}
		}
		.order-pay-right {
			width: 180rpx;
			height: 68rpx;
			background: #d4fee5;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #23c362;
			}
		}
	}
	.order-foot {
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.order-foot-left {
			display: flex;
			justify-content: flex-start;
			align-items: baseline;
			gap: 10rpx;
			text:nth-child(1) {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #1f2638;
			}
			text:nth-child(2) {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #2168fe;
			}
		}
		.order-foot-right {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 20rpx;
			.btn-1 {
				width: 180rpx;
				height: 68rpx;
				background: #f6f7fb;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #8590a7;
				}
			}
			.btn-2 {
				width: 180rpx;
				height: 68rpx;
				background: #2168fe;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #ffffff;
				}
			}
		}
	}
}
.card {
	width: 600rpx;
	height: 530rpx;
	// padding-bottom: 20rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		top: -60rpx;
		image {
			width: 120rpx;
			height: 120rpx;
		}
	}
	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 40rpx;
		color: #1f2638;
		position: relative;
		top: -40rpx;
	}
	.shop {
		width: calc(100% - 50rpx);
		background: #f6f7fb;
		border-radius: 18rpx 18rpx 18rpx 18rpx;
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
		gap: 10rpx;
		.shop-image {
			width: 128rpx;
			height: 128rpx;
			background: #ffffff;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #ebecf0;
		}
		.shop-message {
			margin-top: 10rpx;
			width: calc(100% - 160rpx);
			display: flex;
			flex-direction: column;

			.shop-message-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #1f2638;
				}
			}
			.text {
				margin-top: 10rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #8590a7;

				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.shop-price {
		margin-top: 20rpx;
		width: calc(100% - 50rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		text:nth-child(1) {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #8590a7;
		}
		.text {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			text:nth-child(1) {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #1f2638;
			}
			text:nth-child(2) {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #2168fe;
			}
		}
	}
	.foot {
		margin-top: 46rpx;
		width: calc(100% - 80rpx);
		display: flex;
		justify-content: space-between;
		align-content: center;
		.btn {
			width: 240rpx;
			height: 88rpx;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			border: 2rpx solid #dde1e9;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
		}
		.btn-cancle {
			background: #f6f7fb;

			color: #8590a7;
		}
		.btn-confirm {
			background: #2168fe;
			color: #ffffff;
		}
	}
	.colse {
		position: relative;
		top: 94rpx;
		image {
			width: 80rpx;
			height: 80rpx;
		}
	}
}
</style>
