<template>
	<button open-type="contact" @bindcontact="jumpToWeChatCustomerService">
		<text>客服</text>
	</button>
</template>

<script>
export default {
	methods: {
		// 跳转微信客服
		jumpToWeChatCustomerService() {
			this.openWeChatCustomerService('https://work.weixin.qq.com/kfid/kfc0e0755ec76cf3122', 'ww9c82cbf18bb55e4f');
		}, // 打开微信客服
		openWeChatCustomerService(weiXinCustomerServiceUrl = '', corpId = '', showMessageCard = false, sendMessageTitle = '', sendMessagePath = '', sendMessageImg = '') {
			if (!weiXinCustomerServiceUrl || !corpId) return Toast('请配置好客服链接或者企业ID'); // eslint-disable-next-line no-undef
			wx.openCustomerServiceChat({
				// 客服信息
				extInfo: {
					url: weiXinCustomerServiceUrl // 客服链接 https://work.weixin.qq.com/xxxxxxxx
				},
				corpId, // 企业ID wwed1ca4d3597eXXXX
				showMessageCard, // 是否发送小程序气泡消息
				sendMessageTitle, // 气泡消息标题
				sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
				sendMessageImg, // 气泡消息图片
				success(res) {
					console.log('success', JSON.stringify(res));
				},
				fail(err) {
					console.log('fail', JSON.stringify(err));
					// eslint-disable-next-line no-undef
					return wx.showToast({
						title: err.errMsg,
						icon: 'none'
					});
				}
			});
		}
	}
};
</script>
