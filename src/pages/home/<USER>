<template>
	<view class="container">
		<view
			class="top"
			:style="'min-height:' + statusBarHeight + 'px;'">
			<view
				class="top-content"
				:style="'top:' + top + 'px;' + 'bottom:' + bottom + 'px;' + 'height:' + height + 'px;'">
				<view class="top-card">
					<image
						class="background-image"
						:src="imagebaseurl + 'static/icon/day_sum.png'"
						mode=""></image>
					<label class="title">考研倒计时</label>
					<view class="day-num">
						<label style="font-weight: bold; font-size: 48rpx">{{ examTime }}</label>
						<label style="font-weight: bold; font-size: 24rpx">天</label>
					</view>
				</view>
				<label style="font-weight: bold; font-size: 48rpx; color: #1f2638">立格考研</label>
			</view>
		</view>
		<view
			class="main"
			:style="'position: relative;top:' + (statusBarHeight + 10) + 'px;'">
			<view
				class="notice"
				@click="goPreviousAnnouncements()">
				<view class="left">
					<view class="notice-title">
						<text>公告上新</text>
					</view>
					<text>{{ homeList.announcement.announcementTitle }}</text>
				</view>
				<image
					class="icon-arrow"
					:src="imagebaseurl + 'static/icon/arrow-right.png'"></image>
			</view>
			<!-- 图片轮播 -->
			<view class="u-demo-block">
				<u-swiper
					:list="list"
					keyName="advertPic"
					@change="e => (current = e.current)"
					@click="previewImage"
					:autoplay="false"
					height="300rpx"></u-swiper>
			</view>
			<!-- 指示器 -->
			<view class="indicator">
				<view
					class="indicator__dot"
					v-for="(item, index) in list"
					:key="index"
					:class="[index === current && 'indicator__dot--active']"></view>
			</view>
			<!-- 院校信息 -->
			<view class="college-information">
				<view
					class="college-information-box"
					v-for="(item, index) in college"
					:key="index">
					<image
						class="image"
						:src="imagebaseurl + 'static/icon/background-image_' + (index + 1) + '.png'"
						mode=""></image>
					<view
						class="college-information-message"
						:style="'background:' + item.color + ';'"
						@click="openPicker(item)">
						<text>{{ index === 2 ? "点击查看" : item.content }}</text>
						<u-icon
							name="arrow-down"
							color="white"
							size="16rpx"></u-icon>
					</view>
				</view>
			</view>
			<!-- 菜单选项 -->
			<view class="menu">
				<view
					class="menu-item"
					v-for="(item, index) in menu"
					:key="index"
					@click="goMenItem(item)">
					<image
						class="icon-left"
						:src="imagebaseurl + 'static/icon/menu_item_' + (index + 1) + '.png'"
						mode=""></image>
					<view class="menu-content">
						<text style="font-weight: bold; font-size: 30rpx; color: #212838">{{ item.name }}</text>
						<text style="font-weight: 400; font-size: 24rpx; color: #8590a7">{{ item.content }}</text>
					</view>
				</view>
			</view>
			<!-- 悬浮按钮 -->
			<!-- 	<button class="floating-button" open-type="contact" @bindcontact="jumpToWeChatCustomerService" >
				<image :src="imagebaseurl + 'static/icon/floating_button.png'" mode=""></image>
			</button> -->
			<!-- 悬浮按钮 -->
			<button
				class="floating-button"
				@click="jumpToWeChatCustomerService">
				<image
					:src="imagebaseurl + 'static/icon/floating_button.png'"
					mode=""></image>
			</button>
			<!-- <view  @click="jumpToWeChatCustomerService">
			</view> -->
		</view>
		<view v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				keyName="bizName"
				confirmColor="#2168fe"
				:defaultIndex="[defaultIndex]"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
		<view v-if="showMajor">
			<van-action-sheet
				:show="showMajor"
				:actions="majorList"
				cancel-text="取消"
				:close-on-click-action="false"
				@select="selectMajorClick"
				@cancel="showMajor = false" />
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getAdList, getExamTime, getSchoolTree, getHome, getWechatConfig, queryUserScore, getUserWish } from "../../api/api.js";
export default {
	data() {
		return {
			//状态栏
			statusBarHeight: 0,
			top: 0,
			bottom: 0,
			height: 0,
			//首页
			homeList: {},
			//轮播图数据
			list: [],
			//首页公告
			firstList: {},
			current: 0,
			//院校信息
			college: [
				{ id: 1, name: "院校筛选", color: "#3D4DBD", content: "" },
				{ id: 2, name: "院校信息库", color: "#BB4F41", content: "" },
				{ id: 3, name: "招生简章", color: "#C2760E", content: "" },
			],
			majorList: [],
			showMajor: false,
			//选择的院校信息
			selectCollege: 0,
			show: false,
			columns: [[]],
			show2: false,
			columns2: [[]],
			show3: false,
			columns3: [[]],
			//菜单选项
			menu: [
				{ id: 1, name: "志愿填报", content: "统计与查询", page: "/pages/volunteerRanking/volunteerApplicationStatistics" },
				{ id: 2, name: "讲义背诵", content: "导图技巧", page: "/pages/lectureNotes/lectureNotes" },
				{ id: 3, name: "商城", content: "讲义-真题-串讲" },
				{ id: 4, name: "成绩排名", content: "在线预估名次", page: "/pages/scoreRanking/scoreRanking" },
				{ id: 5, name: "我的课程", content: "统计与查询", page: "/pages/courses/myCourses" },
				{ id: 6, name: "真题库", content: "历年真题", page: "/pages/realQuestionBank/realQuestionBank" },
			],
			//图片基准地址
			imagebaseurl: imagebaseurl,
			//公告列表参数
			params: {
				pageNo: 1,
				pageSize: 10,
				keyWord: "",
			},
			//考研剩余天数
			examTime: 0,
			//院校树
			schoolTreeInfo: [],
			//省份
			selectSchoolInfo: {},
			//学校
			selectSchoolchildren: {},
			//专业
			selectMajor: {},
			user: uni.getStorageSync("user") || {},
			companyId: "",
			customerUrl: "",
			defaultIndex: 0,
			openId: uni.getStorageSync("openId") || "",
			scoreList: [],
			currYear: "",
		};
	},
	created() {
		let curr = new Date().getFullYear();
		this.currYear = curr;
		let that = this;
		uni.getSystemInfo({
			success: res => {
				that.statusBarHeight = 10 + uni.getMenuButtonBoundingClientRect().bottom;
				that.top = uni.getMenuButtonBoundingClientRect().top;
				that.bottom = uni.getMenuButtonBoundingClientRect().bottom;
				that.height = uni.getMenuButtonBoundingClientRect().height;
			},
			fail: err => {
				console.log("err", err);
			},
		});
		this.getAdListInfo();
		//获取考研剩余天数
		getExamTime({ masterType: this.user.masterType })
			.then(res => {
				if (res.result == "1") {
					this.examTime = res.data;
				} else {
					this.$showToast(res.message || "查询异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询异常，请重试");
			});
		//获取首页公告
		getHome({ masterType: this.user.masterType, globalSchoolId: this.user.globalSchoolId })
			.then(res => {
				if (res.result == "1") {
					this.homeList = res.data;
				} else {
					this.$showToast(res.message || "查询异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询异常，请重试");
			});
		//获取学校筛选树
		this.schoolTree();
		//获取微信客服配置
		getWechatConfig({
			masterType: this.user.masterType,
			customerType: 1,
		})
			.then(res => {
				if (res.result == "1") {
					this.companyId = res.data.companyId;
					this.customerUrl = res.data.customerUrl;
				} else {
					this.$showToast(res.message || "微信客服异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询异常，请重试");
			});
	},
	mounted() {},
	methods: {
		checkedLogin() {
			const openid = uni.getStorageSync("openId");
			if (openid == null || openid == undefined || openid == "") {
				this.goLoging();
				return false;
			}
			return true;
		},
		goLoging() {
			// uni.showToast({
			// 	title: "暂未登录，即将前往登录",
			// 	icon: "none",
			// 	success() {
			// 		setTimeout(() => {
			// 			uni.reLaunch({
			// 				url: "/pages/login/phoneLogin",
			// 			});
			// 		}, 500);
			// 	},
			// });
			uni.showModal({
				title: "提示",
				content: "您还未登录，即将前往登录",
				showCancel: true,
				cancelText: "暂不登录",
				success: function (res) {
					if (res.confirm) {
						uni.reLaunch({
							url: "/pages/login/phoneLogin",
						});
					}
				},
			});
		},
		//图片预览
		previewImage() {
			const token = uni.getStorageSync("token");
			if (token == null || token == undefined || token == "") {
				this.goLoging()
				return;
			}
			let currAd = this.list[this.current];
			if (currAd.jumpParam) {
				if (currAd.jumpType == 1) {
					uni.navigateTo({
						url: currAd.jumpParam,
					});

					return;
				} else if (currAd.jumpType == 3) {
					wx.navigateToMiniProgram({
						appId: currAd.jumpParam,
						fail(err) {
							// 打开失败
							console.log(">>打开商城失败>>err>>", err);
							uni.showModal({
								title: "提示",
								content: "跳转失败，请在微信小程序中搜索该小程序",
								showCancel: false,
								success: function (res) {},
							});
						},
						success(res) {
							// 打开成功
						},
					});

					return;
				}
			}

			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});

			uni.previewImage({
				current: this.current,
				urls: urls, // 需要预览的图片http链接列表
				loop: true,
			});
		},
		// 跳转微信客服
		jumpToWeChatCustomerService() {
			this.openWeChatCustomerService(this.customerUrl, this.companyId);
			// this.openWeChatCustomerService('https://work.weixin.qq.com/kfid/kfc0e0755ec76cf3122', 'ww9c82cbf18bb55e4f');
		}, // 打开微信客服
		openWeChatCustomerService(
			weiXinCustomerServiceUrl = "",
			corpId = "",
			showMessageCard = false,
			sendMessageTitle = "",
			sendMessagePath = "",
			sendMessageImg = ""
		) {
			if (!weiXinCustomerServiceUrl || !corpId) return this.$showToast("请配置好客服链接或者企业ID"); // eslint-disable-next-line no-undef
			wx.openCustomerServiceChat({
				// 客服信息
				extInfo: {
					url: weiXinCustomerServiceUrl, // 客服链接 https://work.weixin.qq.com/xxxxxxxx
				},
				corpId, // 企业ID wwed1ca4d3597eXXXX
				showMessageCard, // 是否发送小程序气泡消息
				sendMessageTitle, // 气泡消息标题
				sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
				sendMessageImg, // 气泡消息图片
				success(res) {
					console.log("success", JSON.stringify(res));
				},
				fail(err) {
					console.log("fail", JSON.stringify(err));
					// eslint-disable-next-line no-undef
					return wx.showToast({
						title: err.errMsg,
						icon: "none",
					});
				},
			});
		},
		goMenItem(item) {
			if (item.page) {
				const token = uni.getStorageSync("token");
				if (token == null || token == undefined || token == "") {
					this.goLoging();
					return;
				}
				if (item.id == 1) {
					if (this.checkedLogin()) {
						uni.setStorageSync("startPage", uni.$u.page());
						getUserWish({ openid: this.openId })
							.then(res => {
								if (res.result == "1") {
									if (uni.$u.test.object(res.data) && Object.keys(res.data).length > 0) {
										console.log("用户填报志愿");
										uni.navigateTo({
											url: "/pages/volunteerRanking/volunteerStatistics",
										});
									} else {
										uni.navigateTo({
											url: "/pages/volunteerRanking/volunteerApplicationStatistics",
										});
									}
								} else {
									this.$showToast(res.message || "查询失败，请重试");
								}
							})
							.catch(err => {
								this.$showToast(err.message || "查询异常，请重试");
							});
					}
				} else if (item.id == 4) {
					if (this.checkedLogin()) {
						uni.setStorageSync("startPage", uni.$u.page());
						let openId = uni.getStorageSync("openId");
						// 查询用户成绩历史
						queryUserScore({
							openId: openId,
						})
							.then(res => {
								if (res.result == "1") {
									this.scoreList = res.data;
									if (this.scoreList.length > 0) {
										let year = this.scoreList[0].year;
										if (parseInt(year) == this.currYear) {
											this.$store.commit("updateScoreList", this.scoreList);
											this.$store.commit("updateOpenId", openId);
											uni.navigateTo({
												url: "/pages/scoreRanking/scoreRanking",
											});
										}
									} else {
										uni.navigateTo({
											url: "/pages/scoreRanking/examRankingQuery",
										});
									}
								}
							})
							.catch(err => {
								this.$showToast(err.message || "查询异常，请重试");
							});
					}
				} else if (item.id == 2 || item.id == 5) {
					uni.switchTab({
						url: item.page,
					});
				} else {
					uni.navigateTo({
						url: item.page,
					});
				}
			} else if (item.id == 3) {
				wx.navigateToMiniProgram({
					appId: "wx9c98489e297647e9",
					fail(err) {
						// 打开失败
						console.log(">>打开商城失败>>err>>", err);
						uni.showModal({
							title: "提示",
							content: "跳转失败，请在微信小程序中搜索《立格君》",
							showCancel: false,
							success: function (res) {},
						});
					},
					success(res) {
						// 打开成功
					},
				});
			} else {
				this.$showToast("敬请期待");
			}
		},
		confirm(e) {
			if (e.value[0]) {
				this.college[this.selectCollege - 1].content = e.value[0].bizName;
				if (this.selectCollege == 1) {
					this.selectSchoolInfo = e.value[0];
					this.selectSchoolchildren = {};
					this.selectMajor = {};
					this.college[1].content = "";
				}
				if (this.selectCollege == 2) {
					this.selectSchoolchildren = e.value[0];
					this.selectMajor = {};
					this.college[2].content = "";
					this.show2 = false;
				}
				if (this.selectCollege == 3) {
					this.selectMajor = e.value[0];
					this.show3 = false;
					if (this.selectMajor.bizId == 4) {
						if (this.selectMajor.resetPlan == null || this.selectMajor.resetPlan == "") {
							this.$showToast("暂无数据");
						}
					}
					if (this.selectMajor.bizId == 3) {
						if (this.selectMajor.admissionsCount == null || this.selectMajor.admissionsCount == "") {
							this.$showToast("暂无数据");
						}
					}
					if (this.selectMajor.bizId == 2) {
						if (this.selectMajor.admissionsList == null || this.selectMajor.admissionsList == "") {
							this.$showToast("暂无数据");
						}
					}
					if (this.selectMajor.bizId == 1) {
						if (this.selectMajor.referenceBook == null || this.selectMajor.referenceBook == "") {
							this.$showToast("暂无数据");
						}
					}
				}
			}
			this.show = false;
		},
		openPicker(item) {
			this.selectCollege = item.id;
			//省份
			if (item.id == 1) {
				this.columns[0] = this.schoolTreeInfo;
				if (Object.keys(this.selectSchoolInfo).length > 0) {
					this.defaultIndex = this.schoolTreeInfo.findIndex(item => item.bizId == this.selectSchoolInfo.bizId);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}

				this.show = true;
			}
			//院校
			if (item.id == 2) {
				if (Object.keys(this.selectSchoolInfo).length == 0) {
					this.$showToast("请先选择分类");
					return;
				}
				if (this.selectSchoolInfo.children != null && this.selectSchoolInfo.children.length > 0) {
					this.columns[0] = this.selectSchoolInfo.children;
					if (Object.keys(this.selectSchoolchildren).length > 0) {
						this.defaultIndex = this.selectSchoolInfo.children.findIndex(item => item.bizId == this.selectSchoolchildren.bizId);
						this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
					} else {
						this.defaultIndex = 0;
					}
				} else {
					this.$showToast("该分类暂无院校");
					return;
				}

				this.show = true;
			}
			//招生
			if (item.id == 3) {
				if (Object.keys(this.selectSchoolchildren).length == 0) {
					this.$showToast("请先选择院校");
					return;
				}
				let majorList = [];
				if (this.selectSchoolchildren.referenceBook) {
					majorList.push({
						name: "参考书目",
						fileUrl: this.selectSchoolchildren.referenceBook,
					});
				}
				if (this.selectSchoolchildren.admissionsList) {
					majorList.push({
						name: "招生目录",
						fileUrl: this.selectSchoolchildren.admissionsList,
					});
				}
				if (this.selectSchoolchildren.admissionsCount) {
					majorList.push({
						name: "招生人数",
						fileUrl: this.selectSchoolchildren.admissionsCount,
					});
				}
				if (this.selectSchoolchildren.resetPlan) {
					majorList.push({
						name: "复试方案",
						fileUrl: this.selectSchoolchildren.resetPlan,
					});
				}
				if (majorList.length === 0) {
					this.$showToast("该院校暂无招生简章");
					return;
				}
				this.majorList = majorList;
				this.showMajor = true;
			}
		},
		selectMajorClick(item) {
			let self = this;
			console.log(">>>url>>>", item.detail.fileUrl);
			uni.showLoading({
				title: "加载中",
			});
			wx.downloadFile({
				//将文档下载到本地
				url: item.detail.fileUrl, //pdf链接
				success(res) {
					wx.openDocument({
						//打开文档
						filePath: res.tempFilePath, //本地文档路径
						fileType: "pdf", //文档类型
						showMenu: true,
						success: function (res) {},
						fail: function (res) {
							self.$showToast("查看失败，请重试");
						},
					});
				},
				fail: function (res) {
					self.$showToast("查看失败，请重试");
				},
				complete: function (res) {
					uni.hideLoading();
				},
			});
		},
		goPreviousAnnouncements() {
			const token = uni.getStorageSync("token");
			if (token == null || token == undefined || token == "") {
				this.goLoging();
				return;
			}
			uni.navigateTo({
				url: "/pages/previousAnnouncements/previousAnnouncements",
			});
		},
		//获取院校树
		schoolTree() {
			getSchoolTree({ masterType: this.user.masterType })
				.then(res => {
					if (res.result == "1") {
						this.schoolTreeInfo = res.data;
						this.selectSchoolInfo = res.data[0];
						this.college[0].content = this.selectSchoolInfo.bizName;
						let selfSchool = this.selectSchoolInfo.children.find(item => item.bizId == this.user.globalSchoolId);
						this.selectSchoolchildren = selfSchool || this.selectSchoolInfo.children[0];
						this.college[1].content = this.selectSchoolchildren.bizName;
						if (this.selectSchoolchildren.children != null) {
							this.selectMajor = this.selectSchoolchildren.children[0];
							this.college[0].content = this.selectSchoolInfo.bizName;
						}
					} else {
						this.$showToast(res.message || "院校数据异常，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "院校数据异常，请重试");
				});
		},
		// 获取轮播图数据
		getAdListInfo() {
			this.params.masterType = this.user.masterType;
			this.params.globalSchoolId = this.user.globalSchoolId;
			this.params.advertType = 1;
			getAdList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "院校数据异常，请重试");
				});
		},
	},
};
</script>

<style lang="scss">
@mixin center-row {
	display: flex;
	justify-content: center;
	align-items: center;
}
@mixin center-column {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
@mixin flex-start {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
@mixin space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
::v-deep .u-popup__content {
	border-radius: 38rpx 38rpx 0rpx 9rpx;
}

.floating-button {
	position: fixed;
	right: 10rpx;
	bottom: 13rpx;
	background: none !important;
	image {
		width: 112rpx;
		height: 108rpx;
	}
}

.floating-button::after {
	border: none;
}
.menu {
	margin-top: 30rpx;
	width: calc(100% - 80rpx);
	height: 460rpx;
	@include space-between();
	flex-wrap: wrap;
	gap: 20rpx;
	.menu-item {
		padding: 20rpx;
		width: 284rpx;
		height: 90rpx;
		background: #ffffff;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		@include flex-start();
		gap: 20rpx;
		.icon-left {
			width: 56.81rpx;
			height: 61rpx;
		}
		.menu-content {
			@include center-column();
			align-items: flex-start;
			gap: 8rpx;
		}
	}
}

.college-information {
	margin-top: 30rpx;
	width: calc(100% - 80rpx);
	@include space-between();
	.college-information-box {
		width: 208rpx;
		height: 240rpx;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		position: relative;
		@include center-column();
		.image {
			width: 208rpx;
			height: 240rpx;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
			position: absolute;
			z-index: 0;
		}
		.college-information-message {
			// min-width: 40rpx;
			min-width: 40rpx;
			height: 40rpx;
			padding-left: 10rpx;
			padding-right: 10rpx;
			border-radius: 6rpx 6rpx 6rpx 6rpx;
			z-index: 10;
			position: relative;
			top: -20rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #ffffff;
			@include center-row();
			gap: 5rpx;
		}
	}
}
.indicator {
	@include flex(row);
	justify-content: center;
	margin-top: 10rpx;
	&__dot {
		width: 8rpx;
		height: 8rpx;
		background: #cdd0da;
		border-radius: 4rpx 4rpx 4rpx 4rpx;
		margin: 0 5rpx;
		transition: background-color width 0.3s;

		&--active {
			width: 30rpx;
			height: 8rpx;
			background: #2168fe;
			border-radius: 6rpx 6rpx 6rpx 6rpx;
		}
	}
}

.indicator-num {
	padding: 2px 0;
	background-color: rgba(0, 0, 0, 0.35);
	border-radius: 100px;
	width: 35px;
	@include flex;
	justify-content: center;

	&__text {
		color: #ffffff;
		font-size: 12px;
	}
}
.u-demo-block {
	margin-top: 30rpx;
	width: calc(100% - 69rpx);
}
.notice {
	width: calc(100% - 80rpx);
	padding: 10rpx;
	background: #f6f7fb;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	border: 2rpx solid #c8dcf1;

	@include center-row();
	justify-content: space-between;

	.left {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		font-weight: bold;
		font-size: 24rpx;
		color: #212838;
		.notice-title {
			padding: 10rpx;
			padding-left: 14rpx;
			border-radius: 10rpx;
			background-color: #2168fe;
			font-weight: bold;
			font-size: 22rpx;
			color: #ffffff;
		}
	}
	.icon-arrow {
		margin-right: 10rpx;
		width: 30rpx;
		height: 30rpx;
	}
}
.main {
	width: 100%;
	@include center-column();
	z-index: 9;
}
.container {
	width: 100%;
	min-height: 100vh;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	padding-bottom: 20rpx;
	width: 100%;
	position: fixed;
	top: 0;
	@include center-row();
	z-index: 999;
	background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 176%);
}
.top-content {
	width: calc(100% - 65rpx);
	@include flex-start();
	gap: 108rpx;
	position: fixed;
}
.top-card {
	width: 136rpx;
	height: 108rpx;
	// background: #ffffff;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	@include center-column();
	justify-content: flex-start;
	border-radius: 8rpx;
	overflow: hidden;
	position: relative;
	// left: 10rpx;
	.background-image {
		position: absolute;
		width: 136rpx;
		height: 108rpx;
		border-radius: 8rpx;
		z-index: 0;
	}
	.title {
		z-index: 1;
		width: 100%;
		padding-top: 8rpx;
		// padding-bottom: 10rpx;
		font-weight: bold;
		font-size: 20rpx;
		color: #ffffff;
		@include center-row();
	}
	.day-num {
		z-index: 1;
		@include center-row();
		align-items: baseline;
		color: #2168fe;
	}
}
</style>
