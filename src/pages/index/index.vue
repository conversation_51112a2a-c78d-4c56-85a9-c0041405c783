<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-24 22:08:13
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-01-12 18:35:39
 * @FilePath: /funds_approve_mobile/src/pages/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<view class="flex-col content-view">
		<label class="title-label">{{ currYear }}年政法院校研究生考试排名查询</label>
		<label class="tip-label">* 请认真填写，提交后无法修改</label>
		<view class="cell-view">
			<label class="cell-title">基本信息</label>
			<van-cell-group class="group-view">
				<van-field @input="getNickName" required clearable label="昵称" placeholder="请输入昵称" />
				<van-field :value="userName" @input="getUserName" required clearable label="考生姓名" placeholder="请填写真实姓名" />
			</van-cell-group>
		</view>
		<view class="cell-view">
			<label class="cell-title">专业信息</label>
			<van-cell-group class="group-view">
				<van-cell>
					<van-radio-group @change="masterTypeChange" :value="masterType" class="type-radio-group">
						<van-radio class="type-radio" name="1">学硕</van-radio>
						<van-radio class="type-radio" name="2">专硕</van-radio>
					</van-radio-group>
				</van-cell>

				<van-cell title="学校名称" is-link :value="selectSchool.school" @click="showPicker('s')" />
				<van-cell title="院系名称" is-link :value="selectSchoolDepartment.schoolDepartment" @click="showPicker('d')" />
				<van-cell title="专业名称" is-link :value="selectSchoolProfession.schoolProfession" @click="showPicker('p')" />
			</van-cell-group>
		</view>
		<view class="cell-view">
			<label class="cell-title">成绩信息</label>
			<van-cell-group class="group-view">
				<van-field
					v-for="(item, index) in courseList"
					:key="index"
					:label="item.schoolCourse"
					@input="getCourse"
					:data-index="index"
					placeholder="请输入分数"
					type="digit"
				/>
			</van-cell-group>
		</view>
		<view class="cell-view">
			<label class="cell-title">成绩截图</label>
			<van-cell-group class="score-group-view">
				<UploadFileView :max-count="1" :is-edit="true" :file-list="scoreImages || []" @uploadSuccess="scoreImagesUploadSuccess" @deletImage="deletImageClick" />
				<label class="tip-label">注意：该成绩截图可将身份证号和准考证号关键信息隐去，仅保留姓名与各专业分数作为人工审核真实性依据</label>
			</van-cell-group>
		</view>

		<view class="cell-view">
			<button class="submit-btn" type="primary" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">立即查排名</button>

			<label class="tip-label" style="margin-top: 10rpx; margin-bottom: 50rpx">排名根据实时数据更新，查询完成后可添加到我的小程序，随时查看自己的排名</label>
		</view>

		<van-popup :show="showSchool" :round="true" position="bottom">
			<van-picker @cancel="showPicker('s')" @confirm="selectedSchool" show-toolbar item-height="60" :columns="schoolList" value-key="school" />
		</van-popup>

		<van-popup :show="showDepartment" :round="true" position="bottom">
			<van-picker @cancel="showPicker('d')" @confirm="selectedDepartment" show-toolbar item-height="60" :columns="departmentList" value-key="schoolDepartment" />
		</van-popup>

		<van-popup :show="showProfession" :round="true" position="bottom">
			<van-picker @cancel="showPicker('p')" @confirm="selectedProfession" show-toolbar item-height="60" :columns="professionList" value-key="schoolProfession" />
		</van-popup>
	</view>
</template>

<script>
import {
	requestOpenId,
	requestEncryptedData,
	querySchoolList,
	querySchoolDepartmentList,
	querySchoolProfessionList,
	queryUserScore,
	querySchoolCourseList,
	saveUserScore
} from '../../api/api';
import UploadFileView from '../../component/comment/UploadFileView.vue';
export default {
	data() {
		return {
			currYear: '',
			openId: '',
			sessionKey: '',

			nickName: '',
			userName: '',
			masterType: '',
			phone: '',

			selectSchool: {},
			selectSchoolDepartment: {},
			selectSchoolProfession: {},

			courSe1Score: '',
			courSe2Score: '',
			courSe3Score: '',
			courSe4Score: '',
			totalScore: '',

			schoolList: [],
			departmentList: [],
			professionList: [],
			courseList: [],

			showSchool: false,
			showDepartment: false,
			showProfession: false,

			scoreImages: [],
			scoreImageKeys: [],

			scoreList: [] // 历史成绩
		};
	},

	components: {
		UploadFileView
	},

	onLoad(query) {
		let self = this;
		let curr = new Date().getFullYear();
		this.currYear = curr;
		uni.login({
			provider: 'weixin',
			success: function (loginRes) {
				self.requestOpenIdResp(loginRes.code);
			}
		});

		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline'] // 发送朋友，发送朋友圈
		});
	},

	onShareAppMessage() {
		return {
			title: `立格教育`,
			imageUrl: 'https://qnpb.ligeedu.cn/miniapp_share.jpg',
			success: function (res) {
				console.log('success22:' + JSON.stringify(res));
			},
			fail: function (err) {
				console.log('fail22:' + JSON.stringify(err));
			}
		};
	},

	methods: {
		getNickName(event) {
			this.nickName = event.detail;
		},
		getUserName(event) {
			this.userName = event.detail;
		},
		getCourse(event) {
			if (event.currentTarget.dataset.index == 0) {
				this.courSe1Score = event.detail;
			} else if (event.currentTarget.dataset.index == 1) {
				this.courSe2Score = event.detail;
			} else if (event.currentTarget.dataset.index == 2) {
				this.courSe3Score = event.detail;
			} else if (event.currentTarget.dataset.index == 3) {
				this.courSe4Score = event.detail;
			}
		},
		getNickName(event) {
			this.nickName = event.detail;
		},
		getNickName(event) {
			this.nickName = event.detail;
		},
		// 获取openid
		requestOpenIdResp(code) {
			requestOpenId({
				code: code
			})
				.then((res) => {
					if (res.result == '1') {
						this.openId = res.data.openid;
						this.sessionKey = res.data.sessionKey;
						this.$store.commit('updateOpenId', this.openId);

						this.queryUserScoreResp();
					}
				})
				.catch((err) => {
					this.$showToast(res.message || '授权失败，请重试');
				});
		},

		// 查询用户成绩历史
		queryUserScoreResp() {
			queryUserScore({
				openId: this.openId
			})
				.then((res) => {
					if (res.result == '1') {
						this.scoreList = res.data;

						if (this.scoreList.length > 0) {
							let year = this.scoreList[0].year;

							if (parseInt(year) == this.currYear) {
								this.$store.commit('updateScoreList', this.scoreList);

								uni.reLaunch({
									url: '/pages/score/index'
								});
							}
						}
					}else{
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast('查询失败，请重试');
				});
		},

		showPicker(type) {
			if (type == 's') {
				this.showSchool = !this.showSchool;
			} else if (type == 'd') {
				this.showDepartment = !this.showDepartment;
			} else if (type == 'p') {
				this.showProfession = !this.showProfession;
			}
		},

		selectedSchool(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchool.id) {
				this.selectSchool = value;

				this.selectSchoolDepartment = {};
				this.querySchoolDepartmentListResp();
			}
			this.showSchool = !this.showSchool;
		},
		selectedDepartment(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchoolDepartment.id) {
				this.selectSchoolDepartment = value;

				this.selectSchoolProfession = {};
				this.querySchoolProfessionListResp();
			}
			this.showDepartment = !this.showDepartment;
		},
		selectedProfession(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchoolProfession.id) {
				this.selectSchoolProfession = value;
				this.querySchoolCourseListResp();
			}
			this.showProfession = !this.showProfession;
		},

		// 更改硕士类型
		masterTypeChange(event) {
			this.masterType = event.detail;

			this.selectSchool = {};
			this.selectSchoolDepartment = {};
			this.selectSchoolProfession = {};

			this.querySchoolListResp();
		},
		// 查询学校
		querySchoolListResp() {
			querySchoolList({
				masterType: this.masterType
			})
				.then((res) => {
					if (res.result == '1') {
						this.schoolList = res.data;
					} else {
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast('查询失败，请重试');
				});
		},
		// 查询院系
		querySchoolDepartmentListResp() {
			querySchoolDepartmentList({
				schoolId: this.selectSchool.id
			})
				.then((res) => {
					if (res.result == '1') {
						this.departmentList = res.data;
					} else {
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast('查询失败，请重试');
				});
		},
		// 查询专业
		querySchoolProfessionListResp() {
			querySchoolProfessionList({
				schoolDepartmentId: this.selectSchoolDepartment.id
			})
				.then((res) => {
					if (res.result == '1') {
						this.professionList = res.data;
					} else {
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast('查询失败，请重试');
				});
		},
		// 查询课程
		querySchoolCourseListResp() {
			querySchoolCourseList({
				schoolProfessionId: this.selectSchoolProfession.id
			})
				.then((res) => {
					if (res.result == '1') {
						this.courseList = res.data;
					} else {
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast('查询失败，请重试');
				});
		},

		scoreImagesUploadSuccess(data) {
			this.scoreImages = data.filesUrl;
			this.scoreImageKeys = data.keys;
		},

		deletImageClick() {
			this.scoreImages = [];
			this.scoreImageKeys = [];
		},

		decryptPhoneNumber(event) {
			if (!this.nickName) {
				this.$showToast('请输入昵称');
				return;
			}
			if (!this.userName) {
				this.$showToast('请输入考生姓名');
				return;
			}
			if (!this.masterType) {
				this.$showToast('请选择硕士类型');
				return;
			}
			if (!this.selectSchool.id) {
				this.$showToast('请选择学校');
				return;
			}
			if (!this.selectSchoolDepartment.id) {
				this.$showToast('请选择院系');
				return;
			}
			if (!this.selectSchoolProfession.id) {
				this.$showToast('请选择专业');
				return;
			}
			if (this.courSe1Score.length == 0 || this.courSe2Score.length == 0 || this.courSe3Score.length == 0 || this.courSe4Score.length == 0) {
				this.$showToast('请正确填写分数');
				return;
			}
			if (parseFloat(this.courSe1Score) > 100 || parseFloat(this.courSe1Score) < 1 || parseFloat(this.courSe2Score) > 100 || parseFloat(this.courSe2Score) < 1) {
				this.$showToast('请正确填写分数');
				return;
			}
			if (parseFloat(this.courSe3Score) > 150 || parseFloat(this.courSe3Score) < 1 || parseFloat(this.courSe4Score) > 150 || parseFloat(this.courSe4Score) < 1) {
				this.$showToast('请正确填写分数');
				return;
			}
			if (this.scoreImageKeys.length == 0) {
				this.$showToast('请上传成绩照片');
				return;
			}
			this.totalScore = parseFloat(this.courSe1Score) + parseFloat(this.courSe2Score) + parseFloat(this.courSe3Score) + parseFloat(this.courSe4Score);
			this.requestEncryptedDataResp(event.detail.encryptedData, event.detail.iv);
		},

		// 请求解密手机号
		requestEncryptedDataResp(encryptedData, iv) {
			requestEncryptedData({
				sessionKey: this.sessionKey,
				encryptedData: encryptedData,
				iv: iv
			})
				.then((res) => {
					if (res.result == '1') {
						let data = JSON.parse(res.data);
						this.phone = data.phoneNumber;

						this.saveUserScoreResp();
					} else {
						this.$showToast(res.message || '授权失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast(res.message || '授权失败，请重试');
				});
		},

		saveUserScoreResp() {
			saveUserScore({
				openId: this.openId,
				phone: this.phone,
				nickName: this.nickName,
				userName: this.userName,
				masterType: this.masterType,
				schoolId: this.selectSchool.id,
				schoolDepartmentId: this.selectSchoolDepartment.id,
				schoolProfessionId: this.selectSchoolProfession.id,
				courSe1Score: this.courSe1Score,
				courSe2Score: this.courSe2Score,
				courSe3Score: this.courSe3Score,
				courSe4Score: this.courSe4Score,
				totalScore: `${this.totalScore}`,
				scorePicUrl: this.scoreImageKeys[0]
			})
				.then((res) => {
					if (res.result == '1') {
						uni.reLaunch({
							url: '/pages/score/index'
						});
					} else {
						this.$showToast(res.message || '查询失败，请重试');
					}
				})
				.catch((err) => {
					this.$showToast(res.message || '查询失败，请重试');
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.content-view {
	width: 100vw;
	height: 100vh;
	background-color: rgba($color: #cdbeff, $alpha: 0.1);
	overflow: scroll;
	align-items: center;
	justify-content: flex-start;
	box-sizing: border-box;
	padding: 30rpx;
	.title-label {
		color: #835fff;
		font-size: 40rpx;
		font-weight: bold;
	}
	.tip-label {
		font-size: 28rpx;
		color: #ff4d4f;
		margin-top: 10rpx;
	}
	.cell-view {
		width: 100%;
		margin-top: 30rpx;
		.cell-title {
			font-size: 30rpx;
		}
	}
	.submit-btn {
		margin-top: 40rpx;
		width: 70vw;
		line-height: 80rpx;
		text-align: center;
		font-size: 30rpx;
		background-color: #835fff;
	}
}
::v-deep .van-cell-group {
	margin-top: 20rpx;
	background-color: #fff;
}
::v-deep .score-group-view .van-cell-group {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
}
::v-deep .type-radio .van-radio {
	width: 300rpx;
}
::v-deep .type-radio-group .van-radio-group {
	display: flex;
	flex-direction: row;
}

::v-deep .van-button--primary {
	background: #835fff;
	border: none;
}
</style>
