<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftIconColor="#212838"
				@leftClick="leftClick"
				leftText="成绩录入"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<!-- 图片轮播 -->
			<view
				class="u-demo-block"
				v-if="list.length > 0">
				<u-swiper
					:list="list"
					keyName="advertPic"
					@change="e => (current = e.current)"
					@click="previewImage"
					:autoplay="false"
					height="240rpx"
					radius="20rpx"></u-swiper>
			</view>
			<!-- 指示器 -->
			<view
				class="indicator"
				v-if="list.length > 0">
				<view
					class="indicator__dot"
					v-for="(item, index) in list"
					:key="index"
					:class="[index === current && 'indicator__dot--active']"></view>
			</view>
			<view class="card">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon_1.png'"
						mode=""></image>
					<text>基本信息</text>
				</view>
				<u-line
					length="100%"
					margin="20rpx 0rpx"></u-line>
				<view class="card-content-box">
					<view class="text-box">
						<text class="text-title"> <text style="color: red">*</text>姓名</text>
					</view>
					<view class="input-box">
						<u--input
							placeholder="请填写真实姓名，便于人工核对"
							v-model="userName"
							border="none"></u--input>
					</view>
				</view>
				<view class="card-content-box">
					<view class="text-box">
						<text style="color: red">*</text>
						<text class="text-title">昵称</text>
					</view>

					<view class="input-box">
						<u--input
							placeholder="请输入昵称"
							v-model="user.nickName"
							border="none"
							@change="btnBClick"></u--input>
					</view>
				</view>
			</view>
			<view class="card-select">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon_1.png'"
						mode=""></image>
					<text>专业信息</text>
				</view>
				<u-line
					length="100%"
					margin="20rpx 0rpx"></u-line>
				<view class="card-content-box">
					<view class="text-box">
						<text style="color: red">*</text>
						<text class="text-title">学习形式</text>
					</view>
					<view class="input-box-radion">
						<view
							class="radion-box"
							@click="masterTypeChange(1)">
							<image
								v-if="masterType == 1"
								:src="imagebaseurl + 'static/icon/radion-select.png'"
								mode=""></image>
							<image
								v-if="masterType == 2 || masterType == ''"
								:src="imagebaseurl + 'static/icon/radion-noselect.png'"
								mode=""></image>
							<text :class="masterType == 1 ? 'text-active' : 'text'">学硕</text>
						</view>
						<view
							class="radion-box"
							style="margin-left: 30rpx"
							@click="masterTypeChange(2)">
							<image
								v-if="masterType == 2"
								:src="imagebaseurl + 'static/icon/radion-select.png'"
								mode=""></image>
							<image
								v-if="masterType == 1 || masterType == ''"
								:src="imagebaseurl + 'static/icon/radion-noselect.png'"
								mode=""></image>
							<text :class="masterType == 2 ? 'text-active' : 'text'">专硕</text>
						</view>
					</view>
				</view>
				<view
					class="card-content-box-select"
					style="justify-content: space-around"
					v-for="(item, index) in arrayList"
					:key="item.id">
					<view
						class="text-box"
						style="width: 152rpx">
						<text
							style="color: red"
							v-if="index > 0"
							>*</text
						>
						<text class="text-title">{{ item.name }}</text>
					</view>
					<view
						class="input-box"
						@click="openPicker(item.id)">
						<text>{{ item.content }}</text>
						<image
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
			</view>
			<view class="card-input">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon_3.png'"
						mode=""></image>
					<text>成绩信息</text>
				</view>
				<u-line
					length="100%"
					margin="20rpx 0rpx"></u-line>
				<view
					class="card-content-box-input"
					v-for="(item, index) in courseList"
					:key="item.id">
					<view class="text-box">
						<text style="color: red">*</text>
						<text class="text-title">{{ item.schoolCourse }}</text>
					</view>
					<view
						class="input-box"
						style="width: 454rpx; gap: 30rpx">
						<u--input
							placeholder="请输入分数"
							v-model="item.courSeScore"
							@change="getCourse(item, index)"
							border="none"></u--input>
					</view>
				</view>
			</view>
			<view class="card-upload">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/volunteer_ application_ statistics_icon_4.png'"
						mode=""></image>
					<text>上传照片</text>
				</view>
				<u-line
					length="100%"
					margin="20rpx 0rpx"></u-line>
				<view
					class="card-content-box-upload"
					v-if="fileList.fileUrl == ''"
					@click="showMediaAlert()">
					<view class="upload">
						<image
							style="width: 60.75rpx; height: 54.44rpx"
							:src="imagebaseurl + 'static/icon/upload.png'"
							mode=""></image>
					</view>

					<text><text style="color: red">*</text>上传成绩截图</text>
				</view>
				<view
					class="card-content-box-upload"
					style="margin-top: 0"
					v-else>
					<view class="prewimage-box">
						<image
							class="prewimage"
							:src="fileList.fileUrl"
							@click="prewiewImage(index)"></image>
						<image
							class="delte-icon"
							:src="imagebaseurl + 'static/icon/btn_delet.png'"
							@click="deletePic(index)"></image>
					</view>
				</view>
			</view>
			<view class="foot">
				<view class="foot-top">
					<text style="color: red">*</text>
					<text>注</text>
				</view>
				<text class="remark">1. 该成绩截图可将身份证号和准考证号关键信息隐去，仅保留姓名与各专业分数作为人工审核真实性依据</text>
				<text class="remark">2. 排名根据实时数据更新。</text>
				<text class="remark">3. 恶意填写超3次，拉进黑名单限制使用部分功能。</text>
			</view>
			<view class="btn">
				<button
					v-if="(user.phone == null || user.phone == '') && (pageFrom != 'tab')"
					open-type="getPhoneNumber"
					@getphonenumber="decryptPhoneNumber"
					>立即查排名</button
				>
				<button
					v-else
					@click="decryptPhoneNumber"
					>立即查排名</button
				>
			</view>
		</view>
		<view v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				:keyName="keyName"
				confirmColor="#2168fe"
				:defaultIndex="[defaultIndex]"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
		<van-action-sheet
			:show="showSheet"
			:actions="actions"
			cancel-text="取消"
			close-on-click-action
			@select="onSelect"
			@cancel="onCancel" />
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import {
	checkNickName,
	queryBannerList,
	requestOpenId,
	requestEncryptedData,
	querySchoolList,
	querySchoolDepartmentList,
	querySchoolProfessionList,
	queryUserScore,
	querySchoolCourseList,
	saveUserScore,
	getUploadConfigUrl,
	getSchoolCategoryList,
	getFileUrl,
	getAdList,
	goLogin,
} from "../../api/api";
import UploadFileView from "../../component/comment/UploadFileView.vue";
import { initQiniu, qiniuUploader } from "../../api/qiniu_index.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			arrayList: [
				{ id: 0, name: "学校分类", content: "全部" },
				{ id: 1, name: "学校名称", content: "请选择" },
				{ id: 2, name: "院校名称", content: "请选择" },
				{ id: 3, name: "专业名称", content: "请选择" },
			],
			actions: [{ name: "相册选择" }, { name: "相机" }],
			showSheet: false,
			show: false,
			columns: [[]],
			keyName: "",
			list: [],
			current: 0,
			currYear: "",
			openId: "",
			sessionKey: "",

			nickName: "",
			userName: "",
			masterType: 1,
			phone: "",
			schoolCategory: [],
			selectSchoolCategory: {
				classificationName: "全部",
				id: -1,
			},

			selectSchool: {},
			selectSchoolDepartment: {},
			selectSchoolProfession: {},

			courSe1Score: "",
			courSe2Score: "",
			courSe3Score: "",
			courSe4Score: "",
			totalScore: "",

			schoolList: [],
			departmentList: [],
			professionList: [],
			courseList: [],

			showSchool: false,
			showDepartment: false,
			showProfession: false,

			scoreImages: [],
			scoreImageKeys: [],

			scoreList: [], // 历史成绩

			uptoken: "",
			fileList: {
				fileKey: "",
				fileUrl: "",
			},
			user: {
				nickName: "",
				phone: "",
				masterType: 1,
				globalSchoolId: "",
			},
			defaultIndex: 0,
			checkNickBoolean: false,
			pageFrom: '',
		};
	},
	components: {
		UploadFileView,
	},

	onLoad(query) {
		let self = this;
		let curr = new Date().getFullYear();
		this.currYear = curr;
		let userInfo = uni.getStorageSync("user");
		if (userInfo) {
			this.user.nickName = userInfo.nickName;
			this.user.phone = userInfo.phone;
			this.user.masterType = userInfo.masterType;
			this.user.globalSchoolId = userInfo.globalSchoolId;
			this.openId = userInfo.openId;
		}

		this.pageFrom = query.from || '';

		if (this.openId) {
			this.initData();
		} else {
			uni.login({
				provider: "weixin",
				success: function (loginRes) {
					self.requestOpenIdResp(loginRes.code);
				},
			});
		}

		wx.showShareMenu({
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/scoreRanking/examRankingQuery",
		});
	},

	onShareAppMessage() {
		return {
			title: `立格教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/scoreRanking/examRankingQuery",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},

	mounted() {
		this.getElementHeight();
	},
	methods: {
		previewImage() {
      let currAd = this.list[this.current];
      if (currAd.jumpParam) {
        if (currAd.jumpType == 1) {
          uni.navigateTo({
            url: currAd.jumpParam,
          });

          return;
        }else if (currAd.jumpType == 3) {
          wx.navigateToMiniProgram({
            appId: currAd.jumpParam,
            fail(err) {
              // 打开失败
              console.log(">>打开商城失败>>err>>", err);
              uni.showModal({
                title: "提示",
                content: "跳转失败，请在微信小程序中搜索该小程序",
                showCancel: false,
                success: function (res) {
                }
              });
            },
            success(res) {
              // 打开成功
            }
          })
          
          return;
        }
      }
      
			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});
			uni.previewImage({
				current: this.current,
				urls: [...urls], // 需要预览的图片http链接列表
			});
		},
		initData() {
			this.getAdListInfo();
			this.getSchoolCategory();
			this.querySchoolListResp();
			this.queryUserScoreResp();
		},
		showMediaAlert(index) {
			this.showSheet = true;
			this.selectIndex = index;
		},
		onCancel() {
			this.showSheet = false;
		},
		onSelect(item) {
			let self = this;
			if (item.detail.name == "相册选择") {
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["album"], //从相册选择
					success: function (res) {
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			} else if (item.detail.name == "相机") {
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["camera"], //从相册选择
					success: function (res) {
						console.log(res);
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			}
			this.showSheet = false;
		},
		uploadFileResp(tempFilePaths) {
			var filePath = tempFilePaths[0];
			getUploadConfigUrl({ fileName: "", type: "pic" }).then(res => {
				console.log("七牛云参数：", res);
				initQiniu({ uptoken: res.data.token });
				// 向七牛云上传
				qiniuUploader.upload(
					filePath,
					res => {
						const result = JSON.parse(JSON.stringify(res));
						getFileUrl({ fileKey: result.key, type: "pic" })
							.then(resFile => {
								if (resFile.result == "1") {
									this.fileList.fileKey = result.key;
									this.fileList.fileUrl = resFile.data;
								} else {
									this.$showToast(resFile.message || "成绩截图上传失败请重试");
								}
							})
							.catch(err => {
								this.$showToast(err.message || "成绩截图上传失败请重试");
							});
					},
					error => {
						console.error("error: " + JSON.stringify(error));
						this.$showToast("成绩截图上传失败请重试");
					},
					{
						region: "NCN", // 华北区
						uptokenURL: "",
						domain: "",
						shouldUseQiniuFileName: false,
						key: res.data.fileKey,
						uptokenURL: "",
					}
				);
			});
		},
		// 删除图片
		deletePic() {
			this.fileList = {
				fileKey: "",
				fileUrl: "",
			};
		},
		prewiewImage(index) {
			uni.previewImage({
				urls: [this.fileList.fileUrl],
			});
		},
		leftClick() {
			// uni.navigateBack({ delta: 1 });
			let startPage = uni.getStorageSync("startPage");
			if (uni.getStorageSync("startPage") || startPage != "") {
				uni.switchTab({
					url: uni.getStorageSync("startPage"),
				});
			} else {
				uni.reLaunch({
					url: "/pages/login/phoneLogin",
				});
			}
		},
		btnBClick() {
			// 此处用法为在js中调用，需要写uni.$u.debounce()
			uni.$u.debounce(this.toNext, 1000);
		},
		toNext() {
			checkNickName({ nickName: this.name, type: 1 }).then(res => {
				console.log(res);
				this.checkNickName = res.data;
				if (!this.checkNickName) {
					this.$showToast("昵称已存在");
				}
			});
		},
		confirm(e) {
			if (this.keyName == "classificationName") {
				this.selectSchoolCategory = e.value[0];
				this.schoolList = {};
				this.selectSchoolDepartment = {};
				this.selectSchoolProfession = {};
				this.arrayList = [
					{ id: 0, name: "学校分类", content: "请选择" },
					{ id: 1, name: "学校名称", content: "请选择" },
					{ id: 2, name: "院校名称", content: "请选择" },
					{ id: 3, name: "专业名称", content: "请选择" },
				];
				this.arrayList[0].content = e.value[0].classificationName;
				this.querySchoolListResp();
			}
			if (this.keyName == "school") {
				this.arrayList[1].content = e.value[0].school;
				this.selectSchool = e.value[0];
				this.selectSchoolDepartment = {};
				this.arrayList[2].content = "请选择";
				this.arrayList[3].content = "请选择";
				this.querySchoolDepartmentListResp();
			}
			if (this.keyName == "schoolDepartment") {
				this.arrayList[2].content = e.value[0].schoolDepartment;
				this.selectSchoolDepartment = e.value[0];
				this.selectSchoolProfession = {};
				this.arrayList[3].content = "请选择";
				this.querySchoolProfessionListResp();
			}
			if (this.keyName == "schoolProfession") {
				this.arrayList[3].content = e.value[0].schoolProfession;
				this.selectSchoolProfession = e.value[0];
				this.querySchoolCourseListResp();
			}
			this.show = false;
		},
		openPicker(i) {
			if (i == 0) {
				this.columns[0] = [];
				this.columns[0].push({
					classificationName: "全部",
					id: -1,
				});
				this.columns[0] = [...this.columns[0], ...this.schoolCategory];
				this.keyName = "classificationName";
				if (Object.keys(this.schoolCategory).length > 0) {
					this.defaultIndex = this.schoolCategory.findIndex(item => item.id == this.selectSchoolCategory.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex++;
				} else {
					this.defaultIndex = 0;
				}
			}
			if (i == 1) {
				if (Object.keys(this.selectSchool).length > 0) {
					this.defaultIndex = this.schoolList.findIndex(item => item.id == this.selectSchool.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schoolList;
				this.keyName = "school";
			}
			if (i == 2) {
				if (Object.keys(this.selectSchool).length == 0) {
					this.$showToast("请先选择学校");
					return;
				}
				if (Object.keys(this.selectSchoolDepartment).length > 0) {
					this.defaultIndex = this.departmentList.findIndex(item => item.id == this.selectSchoolDepartment.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.departmentList;
				this.keyName = "schoolDepartment";
			}
			if (i == 3) {
				if (Object.keys(this.selectSchoolDepartment).length == 0) {
					this.$showToast("请先选择院校");
					return;
				}
				if (Object.keys(this.selectSchoolProfession).length > 0) {
					this.defaultIndex = this.professionList.findIndex(item => item.id == this.selectSchoolProfession.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.professionList;
				this.keyName = "schoolProfession";
			}
			this.show = true;
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		getBanner() {
			queryBannerList()
				.then(res => {
					if (res.result == "1") {
						this.list = res.data;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(res => {
					this.$showToast(res.message || "查询失败请重试");
				});
		},
		// 获取轮播图数据
		getAdListInfo() {
			let params = {
				masterType: this.masterType || 1,
				globalSchoolId: this.user.globalSchoolId || -68,
				advertType: 3,
			};
			getAdList(params)
				.then(res => {
					console.log(res);

					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		getCourse(item, index) {
			if (index == 0) {
				this.courSe1Score = item.courSeScore;
			} else if (index == 1) {
				this.courSe2Score = item.courSeScore;
			} else if (index == 2) {
				this.courSe3Score = item.courSeScore;
			} else if (index == 3) {
				this.courSe4Score = item.courSeScore;
			}
		},
		// 获取openid
		requestOpenIdResp(code) {
			requestOpenId({
				code: code,
			})
				.then(res => {
					if (res.result == "1") {
						this.openId = res.data.openid;
						this.sessionKey = res.data.sessionKey;
						this.$store.commit("updateOpenId", this.openId);
						uni.removeStorageSync("openId");
						uni.setStorageSync("openId", this.openId);
						this.getUserInfo();
						this.initData();
					} else if (res.result == "200123") {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "授权失败，请重试");
				});
		},
		getUserInfo() {
			goLogin({ openId: this.openId })
				.then(res => {
					if (res.result == "1") {
						this.user = res.data;
						this.user.openId = this.openId;
						uni.removeStorageSync("user");
						uni.removeStorageSync("token");
						// uni.setStorageSync("openId", that.openId);
						uni.setStorageSync("user", {
							...res.data,
							openId: this.openId,
						});
						uni.setStorageSync("token", res.data.token);
					}
				})
				.catch(err => {
					this.$showToast(res.message || "查询失败请重试");
				});
		},
		// 查询用户成绩历史
		queryUserScoreResp() {
			queryUserScore({
				openId: this.openId,
			})
				.then(res => {
					if (res.result == "1") {
						this.scoreList = res.data;

						if (this.scoreList.length > 0) {
							let year = this.scoreList[0].year;
							if (parseInt(year) == this.currYear) {
								this.$store.commit("updateScoreList", this.scoreList);

								uni.navigateTo({
									url: "/pages/scoreRanking/scoreRanking",
								});
							}
						}
					} else if (res.result == "200123") {
						this.$showToast(res.message || "系统异常，请重试");
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "系统异常，请重试");
				});
		},

		selectedSchool(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchool.id) {
				this.selectSchool = value;

				this.selectSchoolDepartment = {};
				this.querySchoolDepartmentListResp();
			}
			this.showSchool = !this.showSchool;
		},
		selectedDepartment(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchoolDepartment.id) {
				this.selectSchoolDepartment = value;

				this.selectSchoolProfession = {};
				this.querySchoolProfessionListResp();
			}
			this.showDepartment = !this.showDepartment;
		},
		selectedProfession(event) {
			const { value, index } = event.detail;
			if (value.id != this.selectSchoolProfession.id) {
				this.selectSchoolProfession = value;
				this.querySchoolCourseListResp();
			}
			this.showProfession = !this.showProfession;
		},

		// 更改硕士类型
		masterTypeChange(i) {
			this.masterType = i;
			this.selectSchool = {};
			this.selectSchoolDepartment = {};
			this.selectSchoolProfession = {};
			this.arrayList = [
				{ id: 0, name: "学校分类", content: "请选择" },
				{ id: 1, name: "学校名称", content: "请选择" },
				{ id: 2, name: "院校名称", content: "请选择" },
				{ id: 3, name: "专业名称", content: "请选择" },
			];
			this.getSchoolCategory();
			this.querySchoolListResp();
		},
		getSchoolCategory() {
			getSchoolCategoryList({ masterType: this.masterType })
				.then(res => {
					if (res.result == "1") {
						this.schoolCategory = res.data;
						// this.selectSchoolCategory = this.schoolCategory[0];
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "查询失败，请重试");
				});
		},
		// 查询学校
		querySchoolListResp() {
			let params = {
				masterType: this.masterType,
			};
			if (Object.keys(this.selectSchoolCategory).length > 0 && this.selectSchoolCategory.id != -1) {
				params.classificationId = this.selectSchoolCategory.id;
			}
			querySchoolList(params)
				.then(res => {
					if (res.result == "1") {
						this.schoolList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 查询院系
		querySchoolDepartmentListResp() {
			querySchoolDepartmentList({
				schoolId: this.selectSchool.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.departmentList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 查询专业
		querySchoolProfessionListResp() {
			querySchoolProfessionList({
				schoolDepartmentId: this.selectSchoolDepartment.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.professionList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 查询课程
		querySchoolCourseListResp() {
			querySchoolCourseList({
				schoolProfessionId: this.selectSchoolProfession.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.courseList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},

		scoreImagesUploadSuccess(data) {
			this.scoreImages = data.filesUrl;
			this.scoreImageKeys = data.keys;
		},

		deletImageClick() {
			this.scoreImages = [];
			this.scoreImageKeys = [];
		},

		decryptPhoneNumber(event) {
			if (this.userName == "") {
				this.$showToast("请填写真实姓名，便于人工核对");
				return;
			}
			if (!this.checkNickName) {
				this.$showToast("昵称已经存在");
				return;
			}
			if (!this.user.nickName) {
				this.$showToast("请输入昵称");
				return;
			}

			if (!this.masterType) {
				this.$showToast("请选择硕士类型");
				return;
			}
			// if (!this.selectSchoolCategory.id) {
			// 	this.$showToast("请选择学校类别");
			// 	return;
			// }
			if (!this.selectSchool.id) {
				this.$showToast("请选择学校");
				return;
			}
			if (!this.selectSchoolDepartment.id) {
				this.$showToast("请选择院系");
				return;
			}
			if (!this.selectSchoolProfession.id) {
				this.$showToast("请选择专业");
				return;
			}
			if (this.courSe1Score.length == 0 || this.courSe2Score.length == 0 || this.courSe3Score.length == 0 || this.courSe4Score.length == 0) {
				this.$showToast("请正确填写分数");
				return;
			}
			if (
				parseFloat(this.courSe1Score) > 100 ||
				parseFloat(this.courSe1Score) < 1 ||
				parseFloat(this.courSe2Score) > 100 ||
				parseFloat(this.courSe2Score) < 1
			) {
				this.$showToast("请正确填写分数");
				return;
			}
			if (
				parseFloat(this.courSe3Score) > 150 ||
				parseFloat(this.courSe3Score) < 1 ||
				parseFloat(this.courSe4Score) > 150 ||
				parseFloat(this.courSe4Score) < 1
			) {
				this.$showToast("请正确填写分数");
				return;
			}
			if (this.fileList.fileKey == "") {
				this.$showToast("请上传成绩照片");
				return;
			}
			this.totalScore = parseFloat(this.courSe1Score) + parseFloat(this.courSe2Score) + parseFloat(this.courSe3Score) + parseFloat(this.courSe4Score);
			if (this.user.phone) {
				this.saveUserScoreResp();
			} else {
				if (this.pageFrom === 'tab'){
					uni.showModal({
						title: "提示",
						content: "您还未登录，请先登录",
						showCancel: true,
						cancelText: "暂不登录",
						success: function (res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/phoneLogin",
								});
							}
						},
					});
					return;
				}
				this.requestEncryptedDataResp(event.detail.encryptedData, event.detail.iv);
			}
		},

		// 请求解密手机号
		requestEncryptedDataResp(encryptedData, iv) {
			requestEncryptedData({
				sessionKey: this.sessionKey,
				encryptedData: encryptedData,
				iv: iv,
			})
				.then(res => {
					if (res.result == "1") {
						let data = JSON.parse(res.data);
						this.phone = data.phoneNumber;

						this.saveUserScoreResp();
					} else {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "授权失败，请重试");
				});
		},

		saveUserScoreResp() {
			if (this.userName == "") {
				this.$showToast("请输入姓名");
				return;
			}
			this.totalScore = parseFloat(this.courSe1Score) + parseFloat(this.courSe2Score) + parseFloat(this.courSe3Score) + parseFloat(this.courSe4Score);
			saveUserScore({
				openId: this.openId,
				phone: this.phone || this.user.phone,
				nickName: this.user.nickName,
				userName: this.userName,
				masterType: this.masterType,
				schoolId: this.selectSchool.id,
				schoolDepartmentId: this.selectSchoolDepartment.id,
				schoolProfessionId: this.selectSchoolProfession.id,
				courSe1Score: this.courSe1Score,
				courSe2Score: this.courSe2Score,
				courSe3Score: this.courSe3Score,
				courSe4Score: this.courSe4Score,
				totalScore: `${this.totalScore}`,
				scorePicUrl: this.fileList.fileKey,
				classificationId: this.selectSchoolCategory.id,
				classificationName: this.selectSchoolCategory.classificationName,
			})
				.then(res => {
					if (res.result == "1") {
						uni.navigateTo({
							url: "/pages/scoreRanking/scoreRanking",
						});
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "查询失败，请重试");
				});
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-popup__content {
	border-radius: 30rpx;
}
::v-deep .u-toolbar {
	border-bottom: 1rpx solid #edeef2;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 36rpx;
}
::v-deep .u-radio-group {
	flex: 0 !important;
}
.prewimage-box {
	width: 202rpx;
	height: 202rpx;
	postion: relative;
	display: flex;
	justify-content: flex-end;
	.prewimage {
		position: absolute;
		width: 202rpx;
		height: 202rpx;
		border-radius: 20rpx;
	}
	.delte-icon {
		position: relative;
		width: 36rpx;
		height: 36rpx;
		left: 10rpx;
		top: -7rpx;
	}
}
.indicator {
	@include flex(row);
	justify-content: center;
	margin-top: 10rpx;
	&__dot {
		width: 8rpx;
		height: 8rpx;
		background: #cdd0da;
		border-radius: 4rpx 4rpx 4rpx 4rpx;
		margin: 0 5rpx;
		transition: background-color width 0.3s;

		&--active {
			width: 30rpx;
			height: 8rpx;
			background: #2168fe;
			border-radius: 6rpx 6rpx 6rpx 6rpx;
		}
	}
}

.indicator-num {
	padding: 2px 0;
	background-color: rgba(0, 0, 0, 0.35);
	border-radius: 100px;
	width: 35px;
	@include flex;
	justify-content: center;

	&__text {
		color: #ffffff;
		font-size: 12px;
	}
}
.u-demo-block {
	// margin-top: 10rpx;
	width: calc(100% - 69rpx);
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 30rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 100rpx;
	.mian-top {
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 48rpx;
			color: #1f2638;
		}
		image {
			width: 168rpx;
			height: 148rpx;
			margin-right: 26rpx;
		}
	}

	.btn {
		margin-top: 52rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			width: calc(100% - 64rpx);
			height: 98rpx;
			background: #2168fe;
			box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #ffffff;
		}
	}
}
.card {
	margin-top: 30rpx;
	width: calc(100% - 60rpx);
	padding-bottom: 34rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		margin-top: 20rpx;
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
		}
		image {
			width: 27.63rpx;
			height: 23.49rpx;
		}
	}
	.card-content-box {
		margin-top: 30rpx;
		width: calc(100% - 68rpx);
		display: flex;
		justify-content: flex-start;

		gap: 20rpx;
		.text-box {
			width: 95rpx;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			gap: 5rpx;
			.text-title {
				font-weight: bold;
				font-size: 28rpx;
				color: #646d80;
			}
		}

		.input-box {
			padding-left: 22rpx;
			width: calc(100% - 22rpx);
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #1f2638;
			}
			image {
				width: 10.65rpx;
				height: 18.63rpx;
				margin-right: 30rpx;
			}
			.radion-box {
				width: 200rpx;
				height: 70rpx;
				background: #f5f7fa;
				border-radius: 10rpx 10rpx 10rpx 10rpx;
				display: flex;
				justify-content: center;
				align-items: baseline;
				.text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #8590a7;
				}
				.text-active {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #1f2638;
				}
			}
		}
	}
}
.card-select {
	margin-top: 30rpx;
	width: calc(100% - 60rpx);
	padding-bottom: 34rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		margin-top: 20rpx;
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
		}
		image {
			width: 27.63rpx;
			height: 23.49rpx;
		}
	}
	.card-content-box {
		margin-top: 30rpx;
		width: calc(100% - 52rpx);
		display: flex;
		justify-content: space-between;
		gap: 30rpx;
		.text-box {
			width: 206rpx;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			gap: 5rpx;
			.text-title {
				font-weight: bold;
				font-size: 28rpx;
				color: #646d80;
			}
		}

		.input-box-radion {
			width: calc(100% - 22rpx);
			height: 70rpx;
			background: white;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.radion-box {
				width: 200rpx;
				height: 70rpx;
				background: #f5f7fa;
				border-radius: 10rpx 10rpx 10rpx 10rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 20rpx;
				.text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #8590a7;
				}
				.text-active {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #1f2638;
				}
				image {
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
	.card-content-box-select {
		margin-top: 30rpx;
		width: calc(100% - 68rpx);
		display: flex;
		justify-content: space-between;
		gap: 30rpx;
		.text-box {
			width: 95rpx;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			gap: 5rpx;
			.text-title {
				font-weight: bold;
				font-size: 28rpx;
				color: #646d80;
			}
		}

		.input-box {
			padding-left: 22rpx;
			width: calc(100% - 154rpx);
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #1f2638;
			}
			image {
				width: 10.65rpx;
				height: 18.63rpx;
				margin-right: 30rpx;
			}
		}
	}
}
.card-input {
	margin-top: 30rpx;
	width: calc(100% - 60rpx);
	padding-bottom: 34rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		margin-top: 20rpx;
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
		}
		image {
			width: 27.63rpx;
			height: 23.49rpx;
		}
	}
	.card-content-box-input {
		margin-top: 30rpx;
		width: calc(100% - 68rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		.text-box {
			width: 230rpx;
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;
			gap: 5rpx;
			.text-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #646d80;
			}
		}

		.input-box {
			padding-left: 22rpx;
			width: calc(100% - 22rpx);
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
.card-upload {
	margin-top: 30rpx;
	width: calc(100% - 60rpx);
	height: 320rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		margin-top: 20rpx;
		width: calc(100% - 77rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
		}
		image {
			width: 27.63rpx;
			height: 23.49rpx;
		}
	}
	.card-content-box-upload {
		margin-top: 30rpx;
		width: calc(100% - 68rpx);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 26rpx;
			color: #646d80;
		}
		// .image {
		// 	width: 60.75rpx;
		// 	height: 58.39rpx;
		// }
	}
}
.foot {
	margin-top: 20rpx;
	width: calc(100% - 60rpx);
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	.foot-top {
		display: flex;
		gap: 8rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #1f2638;
		}
	}
	.remark {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 20rpx;
		color: #8590a7;
		line-height: 24rpx;
	}
}
</style>