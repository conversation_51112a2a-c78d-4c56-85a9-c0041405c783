<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="成绩排行"
				leftIconColor="#212838"
				@leftClick="leftClick"
				:autoBack="false"
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="tabs">
				<view class="tabs-item tabs-active">
					<text>科目</text>
				</view>
				<view
					class="tabs-item"
					@click="showYear = true">
					<text>{{ yearInfo }}</text>
				</view>
				<view class="tabs-item">
					<text>成绩</text>
				</view>
			</view>
			<view class="table">
				<view class="table-border">
					<view class="tr">
						<view class="td td-r">
							<text>{{ course1.name }}</text>
						</view>
						<view class="td td-l">
							<text>{{ course1.score }}</text>
						</view>
					</view>
					<view class="tr">
						<view class="td td-r">
							<text>{{ course2.name }}</text>
						</view>
						<view class="td td-l">
							<text>{{ course2.score }}</text>
						</view>
					</view>
					<view class="tr">
						<view class="td td-r">
							<text>{{ course3.name }}</text>
						</view>
						<view class="td td-l">
							<text>{{ course3.score }}</text>
						</view>
					</view>
					<view class="tr">
						<view class="td td-r">
							<text>{{ course4.name }}</text>
						</view>
						<view class="td td-l">
							<text>{{ course4.score }}</text>
						</view>
					</view>
					<view class="tr">
						<view class="td td-r">
							<text style="font-weight: 800; color: #1f2638">{{ course5.name }}</text>
						</view>
						<view class="td td-l">
							<text style="color: #2168fe">{{ course5.score }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card-top">
					<view class="card-top-left">
						<view class="card-top-left-top">
							<text>排名</text>
							<view class="people-box">
								<view class="people-icon">
									<image
										:src="imagebaseurl + 'static/icon/people.png'"
										mode=""></image>
								</view>
								<text>学生人数：{{ courseData.totalUserCount }}人</text>
							</view>
						</view>
						<text class="school">在{{ courseData.school }}/{{ courseData.schoolDepartment }}/{{ courseData.schoolProfession }}</text>
					</view>
					<image
						class="huizhang"
						:src="imagebaseurl + 'static/icon/huizhang.png'"
						mode=""></image>
				</view>
				<view class="card-body">
					<view class="card-body-box">
						<view class="text-box">
							<text class="text-box-1">{{ courseData.totalScore }}</text>
							<text class="text-box-2">分</text>
						</view>
						<text class="text-box-3">当前最高分</text>
					</view>
					<view class="card-body-box">
						<view class="text-box">
							<text class="text-box-1">{{ courseData.totalScoreRank }}</text>
							<text class="text-box-2">名</text>
						</view>
						<text class="text-box-3">当前排名</text>
					</view>
					<view class="card-body-box">
						<view class="text-box">
							<text
								class="text-box-1"
								style="color: red"
								>{{ courseData.totalScorePercent }}</text
							>
							<text
								class="text-box-2"
								style="color: red"
								>%</text
							>
						</view>
						<text class="text-box-3">超越其他考生</text>
					</view>
				</view>
				<image
					@click="goProFessionalRanking"
					class="professionalRanking"
					:src="imagebaseurl + 'static/icon/professionalRanking.png'"
					mode=""></image>
			</view>
			<view
				class="table-2"
				v-if="scoreList.length > 0">
				<text class="title">单科成绩排行</text>
				<view class="table-border">
					<view class="table-top">
						<view class="tr">
							<view class="td td-r">
								<text>科目</text>
							</view>
							<view class="td td-c">
								<text>当前排名</text>
							</view>
							<view class="td td-l">
								<text>超越其他考生</text>
							</view>
						</view>
					</view>
					<view class="table-body">
						<view
							class="tr"
							v-for="(item, index) in scoreList"
							:key="index"
							v-if="index < scoreList.length - 1">
							<view class="td td-r">
								<text>{{ item.name }}</text>
							</view>
							<view class="td td-c">
								<text>{{ item.rank }}</text>
							</view>
							<view class="td td-l">
								<text>{{ item.percent }}%</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="qrcode-box">
				<!-- <image
					class="qrcode"
					
					mode=""></image> -->
				<u--image
					width="330rpx"
					height="316rpx"
					:src="courseData.wechatGroupUrl"
					:show-menu-by-longpress="true"
					@click="previewQrCode">
					<view
						slot="error"
						style="font-size: 24rpx"
						>加载失败</view
					>
				</u--image>
				<text>{{ courseData.wechatGroupName || "" }}</text>
			</view>
			<view
				class="foot"
				v-if="list.length > 0">
				<text>更多资讯，欢迎扫码关注</text>
				<view class="u-demo-block">
					<u-swiper
						:list="list"
						keyName="advertPic"
						@change="e => (current = e.current)"
						@click="previewImage"
						:autoplay="false"
						height="368rpx"
						radius="32rpx">
						<view
							slot="indicator"
							class="indicator">
							<view
								class="indicator__dot"
								v-for="(item, index) in list"
								:key="index"
								:class="[index === current && 'indicator__dot--active']"></view>
						</view>
					</u-swiper>
					<u-gap
						bgColor="transparent"
						height="15"></u-gap>
				</view>
			</view>
		</view>
		<!-- 悬浮按钮 -->
		<button
			class="floating-button"
			@click="jumpToWeChatCustomerService">
			<image
				:src="imagebaseurl + 'static/icon/floating_button.png'"
				mode=""></image>
		</button>
		<u-picker
			:show="showYear"
			:columns="yearList"
			@cancel="showYear = false"
			@confirm="confirmYear"></u-picker>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getAdList, queryUserScore, getWechatConfig } from "../../api/api";
import uQRCode from "../../utils/uqrcode.js"; //引入uqrcode.js
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			list: [],
			current: 0,
			yearInfo: "",
			showYear: false,
			iconName: "arrow-down",
			openId: "",
			yearList: [[]],
			courseList: [],
			scoreList: [],
			course1: {},
			course2: {},
			course3: {},
			course4: {},
			course5: {},
			courseData: {},
			qrShow: false,
			homeSlide: [],
			user: uni.getStorageSync("user"),
			companyId: "",
			customerUrl: "",
			masterType: 1,
		};
	},
	onLoad(query) {
		let openId = this.$store.state.openId || uni.getStorageSync("openId");
		let scoreList = this.$store.state.scoreList;

		this.openId = openId;
		if (this.openId) {
			//不走缓存，每次重新获取分数
			// if (scoreList.length == 0) {
			// 	this.queryUserScoreResp();
			// } else {
			// 	this.dealIndexPage(scoreList);
			// }
			this.queryUserScoreResp();
		} else {
			uni.reLaunch({
				url: "/pages/scoreRanking/examRankingQuery",
			});
		}

		wx.showShareMenu({
			title: "立格致远教育",
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/scoreRanking/examRankingQuery",
		});
	},
	onShareAppMessage() {
		return {
			title: `立格致远教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/scoreRanking/examRankingQuery",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},
	mounted() {
		this.getElementHeight();
	},

	methods: {
		previewQrCode() {
			uni.previewImage({
				urls: [this.courseData.wechatGroupUrl],
			});
		},
		//图片预览
		previewImage() {
      let currAd = this.list[this.current];
      if (currAd.jumpParam) {
        if (currAd.jumpType == 1) {
          uni.navigateTo({
            url: currAd.jumpParam,
          });

          return;
        }else if (currAd.jumpType == 3) {
          wx.navigateToMiniProgram({
            appId: currAd.jumpParam,
            fail(err) {
              // 打开失败
              console.log(">>打开商城失败>>err>>", err);
              uni.showModal({
                title: "提示",
                content: "跳转失败，请在微信小程序中搜索该小程序",
                showCancel: false,
                success: function (res) {
                }
              });
            },
            success(res) {
              // 打开成功
            }
          })
          
          return;
        }
      }
      
			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});
			uni.previewImage({
				current: this.current,
				urls: urls, // 需要预览的图片http链接列表
			});
		},
		initData() {
			this.getBanner();
			this.getWechatConfig();
		},
		getWechatConfig() {
			//获取微信客服配置
			getWechatConfig({
				masterType: this.masterType,
				customerType: 2,
			})
				.then(res => {
					if (res.result == "1") {
						this.companyId = res.data.companyId;
						this.customerUrl = res.data.customerUrl;
					} else {
						this.$showToast(res.message || "微信客服配置异常请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "微信客服配置异常请重试");
				});
		},
		// 跳转微信客服
		jumpToWeChatCustomerService() {
			this.openWeChatCustomerService(this.customerUrl, this.companyId);
		}, // 打开微信客服
		openWeChatCustomerService(
			weiXinCustomerServiceUrl = "",
			corpId = "",
			showMessageCard = false,
			sendMessageTitle = "",
			sendMessagePath = "",
			sendMessageImg = ""
		) {
			if (!weiXinCustomerServiceUrl || !corpId) return this.$showToast("请配置好客服链接或者企业ID"); // eslint-disable-next-line no-undef
			wx.openCustomerServiceChat({
				// 客服信息
				extInfo: {
					url: weiXinCustomerServiceUrl, // 客服链接 https://work.weixin.qq.com/xxxxxxxx
				},
				corpId, // 企业ID wwed1ca4d3597eXXXX
				showMessageCard, // 是否发送小程序气泡消息
				sendMessageTitle, // 气泡消息标题
				sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
				sendMessageImg, // 气泡消息图片
				success(res) {
					console.log("success", JSON.stringify(res));
				},
				fail(err) {
					console.log("fail", JSON.stringify(err));
					// eslint-disable-next-line no-undef
					return wx.showToast({
						title: err.errMsg,
						icon: "none",
					});
				},
			});
		},
		leftClick() {
			if (uni.getStorageSync("startPage")) {
				uni.switchTab({
					url: uni.getStorageSync("startPage"),
				});
			} else {
				uni.reLaunch({
					url: "/pages/login/phoneLogin",
				});
			}
			// uni.navigateBack({ delta: 2 });
		},
		confirmYear(e) {
			this.yearInfo = e.value[0];
			this.showYear = false;
		},

		// 查询用户成绩历史
		queryUserScoreResp() {
			queryUserScore({
				openId: this.openId,
			})
				.then(res => {
					if (res.result == "1") {
						this.dealIndexPage(res.data);
						this.$store.commit("updateScoreList", res.data);
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},

		dealIndexPage(list) {
			if (list.length > 0) {
				list.forEach(element => {
					this.yearList[0].push(element.year);
				});
				this.courseList = list;
				this.dealDataInfo(0);
			} else {
				uni.reLaunch({
					url: "/pages/scoreRanking/examRankingQuery",
				});
			}
		},
		dealDataInfo(index) {
			this.courseData = this.courseList[index];
			this.masterType = this.courseData.masterType;
			this.courseData.schoolInfoStr = this.courseData.school + "/" + this.courseData.schoolDepartment + "/" + this.courseData.schoolProfession;
			this.yearInfo = this.courseData.year;

			this.course1.name = this.courseData.course1Name;
			this.course1.score = this.courseData.course1Score;
			this.course1.rank = this.courseData.course1Rank;
			this.course1.percent = this.courseData.course1Percent;

			this.course2.name = this.courseData.course2Name;
			this.course2.score = this.courseData.course2Score;
			this.course2.rank = this.courseData.course2Rank;
			this.course2.percent = this.courseData.course2Percent;

			this.course3.name = this.courseData.course3Name;
			this.course3.score = this.courseData.course3Score;
			this.course3.rank = this.courseData.course3Rank;
			this.course3.percent = this.courseData.course3Percent;

			this.course4.name = this.courseData.course4Name;
			this.course4.score = this.courseData.course4Score;
			this.course4.rank = this.courseData.course4Rank;
			this.course4.percent = this.courseData.course4Percent;

			this.course5.name = "总分";
			this.course5.score = this.courseData.totalScore;

			this.scoreList = [];
			this.scoreList.push(this.course1);
			this.scoreList.push(this.course2);
			this.scoreList.push(this.course3);
			this.scoreList.push(this.course4);
			this.scoreList.push(this.course5);

			this.initData();

			// this.qrFun(this.courseData.wechatGroupUrl);
		},
		// //**生成二维码**//
		// qrFun(text) {
		// 	this.qrShow = true;
		// 	uQRCode.make({
		// 		canvasId: 'qrcode',
		// 		componentInstance: this,
		// 		text: text,
		// 		size: 150,
		// 		margin: 0,
		// 		backgroundColor: '#ffffff',
		// 		foregroundColor: '#000000',
		// 		fileType: 'jpg',
		// 		errorCorrectLevel: uQRCode.errorCorrectLevel.H,
		// 		success: (res) => {}
		// 	});
		// },
		getBanner() {
			getAdList({
				advertType: 3,
				masterType: this.masterType,
			})
				.then(res => {
					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "轮播图异常请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "轮播图异常请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		goProFessionalRanking() {
			uni.navigateTo({
				url:
					"/pages/scoreRanking/professionalRanking?lastRank=" +
					this.courseData.totalScoreRank +
					"&lastTotalScore=" +
					this.courseData.totalScore +
					"&schoolProfession=" +
					this.courseData.schoolProfession,
			});
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.floating-button {
	position: fixed;
	right: 10rpx;
	bottom: 13rpx;
	background: none !important;
	z-index: 999;
	image {
		width: 112rpx;
		height: 108rpx;
	}
}

.floating-button::after {
	border: none;
}
.container {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa -4%, #f6f7fb 26%);
	padding-bottom: 100rpx;
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 38rpx;
	width: 100%;
	padding-bottom: 200rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
	// background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	.tabs {
		width: calc(100% - 80rpx);
		height: 60rpx;
		background: #eff2f7;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		.tabs-item {
			flex: 3;
			background: #eff2f7;
			height: 60rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #8590a7;
		}
		.tabs-active {
			width: 240rpx;
			height: 80rpx;
			background: #ffffff;
			border-radius: 30rpx 30rpx 0rpx 0rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #2168fe;
		}
	}
	.table {
		width: calc(100% - 60rpx);
		height: 400rpx;
		background: #ffffff;
		border-radius: 0rpx 0rpx 30rpx 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.table-border {
			width: calc(100% - 40rpx);
			height: 354rpx;
			background: #ffffff;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			border: 2rpx solid #ebecf0;
			display: flex;
			flex-direction: column;
			align-items: center;
			.tr {
				width: 100%;
				height: 20%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.td {
					flex: 2;
					height: 100%;
					display: flex;
					align-items: center;
					border-bottom: 2rpx solid #ebecf0;
					text {
						font-weight: 500;
						font-size: 24rpx;
						color: #1f2638;
					}
				}
				.td-r {
					border-right: 2rpx solid #ebecf0;
					justify-content: flex-start;
					text {
						margin-left: 50rpx;
					}
				}
				.td-l {
					border-right: 2rpx solid #ebecf0;
					justify-content: center;
				}
			}
		}
	}
	.card {
		margin-top: 20rpx;
		width: calc(100% - 60rpx);
		min-height: 400rpx;
		background: linear-gradient(180deg, #d3e8fb 0%, #ffffff 100%);
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 2rpx solid #ffffff;
		display: flex;
		flex-direction: column;
		align-items: center;
		.card-top {
			width: calc(100% - 90rpx);
			display: flex;
			justify-content: space-between;
			align-items: center;

			.huizhang {
				width: 111.28rpx;
				height: 136.38rpx;
			}
			.card-top-left {
				width: 432rpx;
				// height: 34rpx;
				// margin-top: 40rpx;
				position: relative;
				top: 40rpx;
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.school {
					font-weight: 400;
					font-size: 24rpx;
					color: #646d80;
					padding-bottom: 10rpx;
				}
				.card-top-left-top {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #1f4085;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					gap: 30rpx;
					.people-box {
						width: 260rpx;
						height: 40rpx;
						background: #94b2d6;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						gap: 18rpx;
						text {
							font-weight: bold;
							font-size: 24rpx;
							color: #ffffff;
						}
						.people-icon {
							width: 40rpx;
							height: 44rpx;
							background: #ffffff;
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							image {
								width: 21.55rpx;
								height: 24.05rpx;
							}
						}
					}
				}
			}
		}

		.card-body {
			margin-top: 30rpx;
			width: calc(100% - 90rpx);
			display: flex;
			justify-content: space-between;
			align-items: center;
			.card-body-box {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 10rpx;
				.text-box {
					display: flex;
					justify-content: center;
					align-items: baseline;
					.text-box-1 {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 36rpx;
						color: #2168fe;
					}
					.text-box-2 {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 20rpx;
						color: #2168fe;
					}
				}
				.text-box-3 {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 24rpx;
					color: #1f2638;
				}
			}
		}
		.professionalRanking {
			margin-top: 30rpx;
			width: calc(100% - 60rpx);
			height: 100rpx;
		}
	}
	.table-2 {
		margin-top: 20rpx;
		width: calc(100% - 60rpx);
		height: 500rpx;
		background: #ffffff;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.title {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			width: calc(100% - 40rpx);
			text-align: left;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f4085;
		}
		.table-border {
			width: calc(100% - 58rpx);
			height: 354rpx;
			background: #ffffff;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			border-left: 2rpx solid #ebecf0;

			display: flex;
			flex-direction: column;
			align-items: center;
			.table-top {
				width: 100%;
				height: 20%;
				background: #ebecf0;
				.tr {
					width: 100%;
					height: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.td {
						height: 100%;
						display: flex;
						align-items: center;
						border-bottom: 2rpx solid #ebecf0;
						background: #ebecf0;
						text {
							font-weight: 500;
							font-size: 24rpx;
							color: #1f2638;
						}
					}
					.td-r {
						flex: 3.5;
						border-right: 2rpx solid #e2e2e2;
						justify-content: flex-start;
						text {
							margin-left: 26rpx;
						}
					}
					.td-c {
						flex: 2.4;
						border-right: 2rpx solid #e2e2e2;
						justify-content: center;
					}
					.td-l {
						flex: 3;
						border-right: 2rpx solid #e2e2e2;
						justify-content: center;
					}
				}
			}
			.table-body {
				width: 100%;
				height: 20%;

				.tr {
					width: 100%;
					height: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.td {
						height: 100%;
						display: flex;
						align-items: center;
						border-bottom: 2rpx solid #ebecf0;
						text {
							// width: 190rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 24rpx;
							color: #1f2638;
						}
					}
					.td-r {
						flex: 3.5;
						border-right: 2rpx solid #e2e2e2;
						justify-content: flex-start;
						text {
							margin-left: 26rpx;
						}
					}
					.td-c {
						flex: 2.4;
						border-right: 2rpx solid #e2e2e2;
						justify-content: center;
						text-align: center;
					}
					.td-l {
						flex: 3;
						border-right: 2rpx solid #e2e2e2;
						justify-content: center;
						text-align: center;
					}
				}
			}
		}
	}
	.qrcode-box {
		margin-top: 40rpx;
		// width: 330rpx;
		height: 316rpx;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f2638;
		}
		.qrcode {
			width: 330rpx;
			height: 316rpx;
			background: gray;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
	}
	.foot {
		margin-top: 56rpx;
		width: calc(100% - 60rpx);
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #8590a7;
		}
	}
}
.indicator {
	@include flex(row);
	justify-content: center;
	margin-top: 10rpx;
	&__dot {
		width: 10rpx;
		height: 10rpx;
		background: #cdd0da;
		border-radius: 50%;
		margin: 0 5rpx;
		transition: background-color width 0.3s;

		&--active {
			width: 10rpx;
			height: 10rpx;
			background: #323232;
			border-radius: 6rpx 6rpx 6rpx 6rpx;
		}
	}
}

.indicator-num {
	padding: 2px 0;
	background-color: rgba(0, 0, 0, 0.35);
	border-radius: 100px;
	width: 35px;
	@include flex;
	justify-content: center;

	&__text {
		color: #ffffff;
		font-size: 12px;
	}
}
.u-demo-block {
	margin-top: 20rpx;
	width: calc(100%);
}
</style>
