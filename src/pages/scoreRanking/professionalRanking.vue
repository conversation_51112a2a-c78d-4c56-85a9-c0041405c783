<template>
	<view class="container">
		<view class="top">
			<u-navbar
				:leftText="schoolProfession || '专业排名'"
				title=" "
				:bgColor="bgColor"
				:autoBack="true"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="main-top-image">
				<u-swiper
					:list="list"
					keyName="advertPic"
					@change="e => (current = e.current)"
					@click="previewImage"
					:autoplay="false"
					height="282rpx"
					round="30rpx"></u-swiper>
			</view>
			<!-- <image class="main-top-image" :src="" mode=""></image> -->
			<view class="table-box">
				<view class="table">
					<view class="tr">
						<view
							class="tb tb-r"
							style="width: 127rpx">
							<text>昵称</text>
						</view>
						<!-- 	<view
							class="tb"
							style="width: 141rpx">
							<text>专业</text>
						</view> -->

						<view
							class="tb"
							style="width: 168rpx; display: flex; flex-direction: column">
							<text
								class="clamp"
								style="width: 128rpx"
								>{{ scoreList[0].course1Name == "思想政治理论" ? "思政" : scoreList[0].course1Name }}</text
							>
							<text
								class="clamp"
								style="width: 128rpx"
								>{{ scoreList[0].course2Name }}</text
							>
						</view>
						<view
							class="tb"
							style="width: 168rpx; display: flex; flex-direction: column">
							<text
								class="clamp"
								style="width: 118rpx"
								>业务课一</text
							>
							<text
								class="clamp"
								style="width: 118rpx"
								>业务课二</text
							>
						</view>
						<view
							class="tb"
							style="width: 101rpx">
							<text>总分</text>
						</view>
						<view
							class="tb"
							style="width: 101rpx">
							<text>排名</text>
						</view>

						<view
							class="tb tb-l"
							style="width: 138rpx">
							<text>一键举报</text>
						</view>
					</view>
					<view
						class="tr"
						style="background-color: white"
						v-for="(item, index) in scoreList"
						:key="index">
						<view
							class="tb"
							style="width: 127rpx">
							<text class="userNameClamp">{{ item.nickName }}</text>
						</view>

						<view
							class="tb"
							style="width: 168rpx">
							<text
								class="clamp text"
								style="width: 100%; text-align: center"
								>{{ item.course1Score }}/{{ item.course2Score }}</text
							>
						</view>
						<view
							class="tb"
							style="width: 168rpx">
							<text
								class="clamp text"
								style="width: 100%; text-align: center"
								>{{ item.course3Score }}/{{ item.course4Score }}</text
							>
						</view>
						<view
							class="tb"
							style="width: 101rpx">
							<text class="text">{{ item.totalScore }}</text>
						</view>
						<view
							class="tb"
							style="width: 101rpx">
							<text
								class="text"
								style="color: #2168fe"
								>{{ item.rank }}</text
							>
						</view>

						<view
							class="tb"
							style="width: 138rpx">
							<image
								class="btn-icon"
								:src="imagebaseurl + 'static/icon/report_icon.png'"
								@click="report(item)"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-popup
			:show="show"
			@close="close"
			@open="open"
			mode="center"
			:safeAreaInsetBottom="fasle"
			:safeAreaInsetTop="false">
			<view class="card">
				<view class="card-top">
					<image
						:src="imagebaseurl + 'static/icon/report_radius_icon.png'"
						mode=""></image>
				</view>
				<text class="title">一键举报</text>
				<text class="content">分数存在造假可“确定”提交，经工作人员核实，将重新排名!</text>
				<text class="tip">注：虚假举报将被限制使用部分功能</text>
				<view class="foot">
					<view
						class="btn btn-cancle"
						@click="show = false">
						<text>取消</text>
					</view>
					<view
						class="btn btn-confirm"
						@click="submitReportScore">
						<text>确定</text>
					</view>
				</view>
				<view class="colse">
					<image
						:src="imagebaseurl + 'static/icon/close_icon.png'"
						mode=""
						@click="show = false"></image>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getAdList, queryUserScore, getScoreRanking, reportScore } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			//图片基准地址
			imagebaseurl: imagebaseurl,
			show: false,
			list: [],
			user: uni.getStorageSync("user"),
			//公告列表参数
			params: {
				pageNo: 1,
				pageSize: 10,
				keyWord: "",
			},
			scoreList: [],
			lastRank: "",
			lastTotalScore: "",
			schoolProfession: "",
			reportItem: {},
			userSCcore: [],
			openId: "",
			tableData: [],
			current: 0,
		};
	},
	onLoad(option) {
		this.lastRank = option.lastRank;
		this.lastTotalScore = option.lastTotalScore;
		this.schoolProfession = option.schoolProfession;
		this.userSCcore = this.$store.state.scoreList;
		this.openId = this.$store.state.openId || uni.getStorageSync("openId");
		if (this.userSCcore.length == 0) {
			uni.reLaunch({
				url: "/pages/scoreRanking/examRankingQuery",
			});
		}
		wx.showShareMenu({
			title: "立格致远教育",
			withShareTicket: true,
			menus: ["shareAppMessage", "shareTimeline"], // 发送朋友，发送朋友圈
			path: "/pages/scoreRanking/examRankingQuery",
		});
	},
	mounted() {
		this.getElementHeight();
		this.getAdListInfo();
		// this.queryUserScoreResp();
		this.getScoreRankings();
	},
	onShareAppMessage() {
		return {
			title: `立格致远教育`,
			imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
			path: "/pages/scoreRanking/examRankingQuery",
			success: function (res) {
				console.log("success22:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail22:" + JSON.stringify(err));
			},
		};
	},
	methods: {
		previewImage() {
      let currAd = this.list[this.current];
      if (currAd.jumpParam) {
        if (currAd.jumpType == 1) {
          uni.navigateTo({
            url: currAd.jumpParam,
          });

          return;
        }else if (currAd.jumpType == 3) {
          wx.navigateToMiniProgram({
            appId: currAd.jumpParam,
            fail(err) {
              // 打开失败
              console.log(">>打开商城失败>>err>>", err);
              uni.showModal({
                title: "提示",
                content: "跳转失败，请在微信小程序中搜索该小程序",
                showCancel: false,
                success: function (res) {
                }
              });
            },
            success(res) {
              // 打开成功
            }
          })
          
          return;
        }
      }
      
			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});
			uni.previewImage({
				current: this.current,
				urls: [...urls], // 需要预览的图片http链接列表
			});
		},
		// 提交举报
		submitReportScore() {
			reportScore({
				masterType: this.userSCcore[0].masterType,
				id: this.reportItem.id,
				openid: this.openId,
			})
				.then(res => {
					console.log("举报结果", res);
					if (res.result == "1") {
						this.$showToast("举报成功");
						this.show = false;
						let index = this.scoreList.findIndex(item => item.id == this.reportItem.id);
						if (index > -1) {
							this.scoreList[index].isReport = 1;
						}
					} else if (res.result == "20007") {
						// uni.showModal({
						// 	title: "提示",
						// 	content: "您还未注册，请先注册",
						// 	success: res => {
						// 		if (res.confirm) {
						// 			uni.reLaunch({
						// 				url: "/pages/login/phoneLogin",
						// 			});
						// 		}
						// 	},
						// });
						uni.showModal({
							title: "提示",
							content: "您还未注册，请先注册",
							showCancel: true,
							cancelText: "暂不注册",
							success: function (res) {
								if (res.confirm) {
									uni.reLaunch({
										url: "/pages/login/phoneLogin",
									});
								}
							},
						});
					} else {
						this.$showToast(res.message || "举报失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "举报失败请重试");
				});
		},
		getScoreRankings() {
			getScoreRanking({
				masterType: this.userSCcore[0].masterType,
				// masterType: 2,
				lastRank: this.lastRank,
				lastTotalScore: this.lastTotalScore,
				openId: this.openId,
				pageSize: 999,
			})
				.then(res => {
					console.log("----------------", res);
					if (res.result == "1") {
						this.scoreList = res.data.list;
						console.table(res.data.list, ["course1Name", "course2Name", "course3Name", "course4Name"]);
					} else {
						this.$showToast(res.message || "获取排名失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取排名失败");
				});
		},
		// 查询用户成绩历史
		queryUserScoreResp() {
			// let openId = this.$store.state.openId || uni.getStorageSync("openId");
			queryUserScore({
				openId: this.openId,
			})
				.then(res => {
					if (res.result == "1") {
						this.scoreList = res.data;
					} else {
						this.$showToast(res.message || "获取成绩失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取成绩失败");
				});
		},
		// 获取轮播图数据
		getAdListInfo() {
			this.params.masterType = this.userSCcore[0].masterType;
			this.params.globalSchoolId = this.userSCcore[0].schoolId;
			this.params.advertType = 3;
			getAdList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "获取轮播图失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取轮播图失败请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		open() {
			// console.log('open');
			// this.$showToast("虚假举报将被限制使用部分功能");
		},
		close() {
			this.show = false;
			// console.log('close');
		},
		report(item) {
			if (item.isReport == 1) {
				this.$showToast("该成绩已被举报，您可以关注后续排名变化");
				return;
			}
			this.show = true;
			this.reportItem = item;
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.userNameClamp {
	width: 100rpx;
	text-align: center;
	white-space: normal;
	word-wrap: break-word;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
	text-overflow: ellipsis;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 24rpx;
	color: #1f2638;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
}
::v-deep .uni-table-th {
	display: table-cell;
	box-sizing: border-box;
	color: #909399;
	border: 1rpx solid #ebecf0;
	background: #f6f7fb;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400 !important;
	font-size: 24rpx !important;
	color: #8590a7 !important;
	height: 97rpx !important;
}
.main {
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
	.main-top-image {
		margin-top: 26rpx;
		width: calc(100% - 80rpx);
		height: 282rpx;
		overflow: hidden;
		// background: #ce6464;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
	}
	.table-box {
		margin-top: 30rpx;
		width: 100%;
		height: 100vh;
		background: #ffffff;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.table {
			margin-top: 20rpx;
			width: calc(100% - 41rpx);
			// height: 354rpx;
			background: #ffffff;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			.tr {
				width: calc(100%);
				min-height: 74rpx;
				background: #f6f7fb;
				border-radius: 16rpx 16rpx 0rpx 0rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.tb {
					width: 100rpx;
					min-height: 97rpx;
					border: 1rpx solid #ebecf0;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #8590a7;
				}
				.tb-r {
					border-radius: 16rpx 0rpx 0rpx 0rpx;
				}
				.tb-l {
					border-radius: 0rpx 16rpx 0rpx 0rpx;
				}
			}
			.tbody {
				width: calc(100%);
				height: 74rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.tbody-tr {
					width: calc(100%);
					height: 74rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					.tbody-tb {
						// width: 100rpx;
						width: 92rpx;
						height: 74rpx;
						border: 1rpx solid #ebecf0;
						display: flex;
						justify-content: center;
						align-items: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 24rpx;
						color: #1f2638;
						.btn-icon {
							width: 31.85rpx;
							height: 36.42rpx;
						}
					}
				}
			}
		}
	}
}
.btn-icon {
	width: 31.85rpx;
	height: 36.42rpx;
}
.text {
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 24rpx;
	color: #1f2638;
}
.clamp {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	// width: 188rpx;
	text-align: center;
}
.card {
	width: 600rpx;
	height: 440rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card-top {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		top: -60rpx;
		image {
			width: 120rpx;
			height: 120rpx;
		}
	}
	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 40rpx;
		color: #1f2638;
		position: relative;
		top: -40rpx;
	}
	.content {
		position: relative;
		top: -20rpx;
		width: calc(100% - 80rpx);
		height: 88rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 28rpx;
		color: #1f2638;
		line-height: 48rpx;
	}
	.tip {
		width: calc(100% - 80rpx);
		font-size: 23rpx;
		color: #a8abb1;
	}
	.foot {
		margin-top: 30rpx;
		width: calc(100% - 80rpx);
		display: flex;
		justify-content: space-between;
		align-content: center;
		.btn {
			width: 240rpx;
			height: 88rpx;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			border: 2rpx solid #dde1e9;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
		}
		.btn-cancle {
			background: #f6f7fb;

			color: #8590a7;
		}
		.btn-confirm {
			background: #2168fe;
			color: #ffffff;
		}
	}
	.colse {
		position: relative;
		top: 94rpx;
		image {
			width: 80rpx;
			height: 80rpx;
		}
	}
}
</style>
