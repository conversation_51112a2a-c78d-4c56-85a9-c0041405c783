<template>
	<view class="container">
		<view class="top" :style="'min-height:' + statusBarHeight + 'px;'">
			<view class="top-content" :style="'top:' + top + 'px;' + 'bottom:' + bottom + 'px;' + 'height:' + height + 'px;'">
				<view class="top-card">
					<view class="college-information-message" @click="openPicker">
						<text>西北政法</text>
						<u-icon name="arrow-down" color="white" size="16rpx"></u-icon>
					</view>
				</view>
				<label style="font-weight: bold; font-size: 48rpx; color: #1f2638">立格商城</label>
			</view>
		</view>
		<view class="main" :style="'position: relative;top:' + (statusBarHeight + 10) + 'px;'">
			<view class="notice" @click="goPreviousAnnouncements()">
				<view class="left">
					<view class="notice-title">
						<text>上架公告</text>
					</view>
					<text>2025招生简章，火热出炉!</text>
				</view>
				<image class="icon-arrow" :src="imagebaseurl + 'static/icon/arrow-right.png'"></image>
			</view>
			<!-- 图片轮播 -->
			<view class="u-demo-block">
				<u-swiper :list="list" @change="(e) => (current = e.current)" :autoplay="false" height="300rpx"></u-swiper>
			</view>
			<!-- 指示器 -->
			<view class="indicator">
				<view class="indicator__dot" v-for="(item, index) in list" :key="index" :class="[index === current && 'indicator__dot--active']"></view>
			</view>
			<!-- 全部分类 -->
			<view class="menu">
				<text class="menu-title">全部分类</text>
				<view class="menu-item-box">
					<view class="menu-item" v-for="(item, index) in menu" :key="index">
						<image class="icon-left" :src="item.src" mode=""></image>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</view>
		</view>
		<u-picker :show="show" :columns="columns" confirmColor="#2168fe" @cancel="show = false" @confirm="show = false"></u-picker>
	</view>
</template>

<script>
import { imagebaseurl } from '../../api/index.js';
import { getAdList } from '../../api/api.js';
export default {
	data() {
		return {
			statusBarHeight: 0, //状态栏高度
			top: 0,
			bottom: 0,
			height: 0,
			//轮播图数据
			list: ['https://cdn.uviewui.com/uview/swiper/swiper3.png', 'https://cdn.uviewui.com/uview/swiper/swiper2.png', 'https://cdn.uviewui.com/uview/swiper/swiper1.png'],
			current: 0,
			//院校信息
			show: false,
			columns: [['陕西省', '西北政法大学']],
			menu: [
				{ id: 1, name: '799/全年包', src: imagebaseurl + 'static/icon/menu_item_3.png' },
				{ id: 2, name: '我的订单', src: imagebaseurl + 'static/icon/menu_item_10.png' },
				{ id: 3, name: '在线数据统计', src: imagebaseurl + 'static/icon/menu_item_7.png' },
				{ id: 4, name: '真题库', src: imagebaseurl + 'static/icon/menu_item_6.png' },
				{ id: 5, name: '我的课程', src: imagebaseurl + 'static/icon/menu_item_5.png' },
				{ id: 6, name: '我的收藏', src: imagebaseurl + 'static/icon/menu_item_8.png' },
				{ id: 7, name: '我的讲义', src: imagebaseurl + 'static/icon/menu_item_9.png' }
			],
			//图片基准地址
			imagebaseurl: imagebaseurl,
			params: {
				pageNo: 1,
				pageSize: 10,
				keyWord: '',
				masterType: 1,
				globalSchoolId: 1
			}
		};
	},
	created() {
		let that = this;
		uni.getSystemInfo({
			success: (res) => {
				console.log(uni.getMenuButtonBoundingClientRect());
				that.statusBarHeight = 10 + uni.getMenuButtonBoundingClientRect().bottom;
				that.top = uni.getMenuButtonBoundingClientRect().top;
				that.bottom = uni.getMenuButtonBoundingClientRect().bottom;
				that.height = uni.getMenuButtonBoundingClientRect().height;
			},
			fail: (err) => {
				console.log('err', err);
			}
		});
		this.getAdListInfo();
	},
	mounted() {},
	methods: {
		openPicker() {
			this.show = true;
		},
		goPreviousAnnouncements() {
			uni.navigateTo({
				url: '/pages/previousAnnouncements/previousAnnouncements'
			});
		},
		// 获取轮播图数据
		getAdListInfo() {
			getAdList(this.params).then((res) => {
				console.log(res);
			});
		}
	}
};
</script>

<style lang="scss">
@mixin center-row {
	display: flex;
	justify-content: center;
	align-items: center;
}
@mixin center-column {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
@mixin flex-start {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
@mixin space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
::v-deep .u-popup__content {
	border-radius: 38rpx 38rpx 0rpx 9rpx;
}
.menu {
	margin-top: 20rpx;
	width: calc(100% - 60rpx);
	height: 460rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.menu-title {
		padding: 20rpx;
		width: calc(100% - 60rpx);
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 32rpx;
		color: #1f2638;
	}
	.menu-item-box {
		margin-top: 20rpx;
		width: calc(100% - 47rpx);
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-items: center;
		gap: 56rpx;

		.menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10rpx;
			image {
				width: 80rpx;
				height: 80rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #1f2638;
			}
		}
	}
}
.indicator {
	@include flex(row);
	justify-content: center;
	margin-top: 10rpx;
	&__dot {
		width: 8rpx;
		height: 8rpx;
		background: #cdd0da;
		border-radius: 4rpx 4rpx 4rpx 4rpx;
		margin: 0 5rpx;
		transition: background-color width 0.3s;

		&--active {
			width: 30rpx;
			height: 8rpx;
			background: #2168fe;
			border-radius: 6rpx 6rpx 6rpx 6rpx;
		}
	}
}

.indicator-num {
	padding: 2px 0;
	background-color: rgba(0, 0, 0, 0.35);
	border-radius: 100px;
	width: 35px;
	@include flex;
	justify-content: center;

	&__text {
		color: #ffffff;
		font-size: 12px;
	}
}
.u-demo-block {
	margin-top: 30rpx;
	width: calc(100% - 69rpx);
}
.notice {
	width: calc(100% - 80rpx);
	padding: 10rpx;
	background: #f6f7fb;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	border: 2rpx solid #c8dcf1;

	@include center-row();
	justify-content: space-between;

	.left {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		font-weight: bold;
		font-size: 24rpx;
		color: #212838;
		.notice-title {
			padding: 10rpx;
			padding-left: 14rpx;
			border-radius: 10rpx;
			background-color: #2168fe;
			font-weight: bold;
			font-size: 22rpx;
			color: #ffffff;
		}
	}
	.icon-arrow {
		margin-right: 10rpx;
		width: 30rpx;
		height: 30rpx;
	}
}
.main {
	width: 100%;
	@include center-column();
	z-index: 9;
}
.container {
	width: 100%;
	min-height: 100vh;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	padding-bottom: 20rpx;
	width: 100%;
	position: fixed;
	top: 0;
	@include center-row();
	z-index: 999;
	background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 176%);
}
.top-content {
	width: calc(100% - 65rpx);
	@include flex-start();
	gap: 84rpx;
	position: fixed;
}
.top-card {
	width: 156rpx;
	height: 48rpx;
	background: #94b2d6;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	@include center-row();
	.college-information-message {
		font-weight: bold;
		font-size: 24rpx;
		color: #ffffff;
		@include center-row();
		gap: 5rpx;
	}
}
</style>
