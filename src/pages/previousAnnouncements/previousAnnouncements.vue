<template>
	<view class="container">
		<view
			class="top"
			:style="'height:' + statusBarHeight + 'px;'">
			<u-navbar
				leftText="往期公告"
				autoBack
				title=" "
				:border="false"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'position: relative;top:' + (statusBarHeight + 10) + 'px;'">
			<view class="search">
				<view class="search-content">
					<image
						:src="imagebaseurl + 'static/icon/search-icon.png'"
						mode=""></image>
					<u--input
						placeholder="搜索"
						border="none"
						v-model="params.keyWord"
						@change="search"></u--input>
				</view>
			</view>
			<view
				class="content"
				v-for="(item, index) in list"
				:key="index"
				@click="previewImage(item.announcementDesc)">
				<view class="content-top">
					<view class="content-top-box">
						<view class="triangle"></view>
						<text class="clamp">{{ item.announcementTitle }}</text>
					</view>
					<view class="btn">
						<text>更多</text>
						<image
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
				<image
					class="content-body"
					:src="item.announcementCover"
					mode=""></image>
				<view class="content-foot">
					<image
						:src="imagebaseurl + 'static/icon/time-icon.png'"
						mode=""></image>
					<text>{{ item.updateTime }}</text>
				</view>
			</view>

			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
import { getAnnouncementList } from "../../api/api.js";
import { imagebaseurl } from "../../api/index.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			statusBarHeight: 0,
			topHeight: 0,
			list: [],
			params: {
				pageNo: 1,
				pageSize: 10,
				advertType: 1, //广告类型 1 首页广告 2 志愿广告 3排名广告
			},
			imagebaseurl: imagebaseurl,
			debounceTimeout: null,
			user: uni.getStorageSync("user") || {},
			status: "loadmore",
			loadingText: "努力加载中",
			loadmoreText: "轻轻上拉",
			nomoreText: "实在没有了",
		};
	},
	created() {
		let that = this;
		uni.getSystemInfo({
			success: res => {
				that.statusBarHeight = 10 + uni.getMenuButtonBoundingClientRect().bottom;
			},
			fail: err => {
				console.log("err", err);
			},
		});
		this.getList();
	},
	mounted() {
		this.getElementHeight();
	},
	beforeDestroy() {
		// 组件销毁前清除延时器，避免内存泄漏
		if (this.debounceTimeout) {
			clearTimeout(this.debounceTimeout);
		}
	},
	onPullDownRefresh() {
		this.params = {
			pageNo: 1,
			pageSize: 10,
			keyWord: "",
			advertType: 1, //广告类型 1 首页广告 2 志愿广告 3排名广告
		};
		this.list = [];
		this.getList();
		uni.stopPullDownRefresh();
	},
	onReachBottom() {
		this.status = "loading";
		this.params.pageNo++;
		this.getList();
	},
	methods: {
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		//获取公告列表
		getList() {
			this.params.masterType = this.user.masterType;
			this.params.globalSchoolId = this.user.globalSchoolId;
			getAnnouncementList(this.params)
			.then(res => {
				if (res.result == "1") {
					this.list = [...this.list, ...res.data.list];
					this.status = "nomore";
				} else {
					this.$showToast(res.message || "查询失败请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询失败请重试");
			});

		},
		//搜索
		search() {
			// 清除上一次的延时器
			if (this.debounceTimeout) {
				clearTimeout(this.debounceTimeout);
			}
			// 设置新的延时器
			this.debounceTimeout = setTimeout(() => {
				this.list = [];
				this.params.pageNo = 1;
				this.params.pageSize = 10;
				this.getList();
			}, 300); // 设置延时300毫秒
		},
		previewImage(url) {
			// uni.setStorageSync("webviewUrl", url);
			// uni.navigateTo({
			// 	url: "/pages/webview/webview?type=1",
			// });
			let self = this;
			console.log(">>>url>>>", url)
			uni.showLoading({
				title: '加载中',
			});
			wx.downloadFile({ //将文档下载到本地
				url: url,//pdf链接
				success(res) {
					wx.openDocument({ //打开文档
						filePath: res.tempFilePath,//本地文档路径
						fileType: "pdf",//文档类型
						showMenu: true,
						success: function (res) {
							
						},
						fail: function (res) {
							self.$showToast('查看失败，请重试')
						},
					})
				},
				fail: function (res) {
					self.$showToast('查看失败，请重试')
				},
				complete: function (res) {
					uni.hideLoading();
				}
			})
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-icon__icon {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	// background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	background: linear-gradient(180deg, #bbddfa 8%, #f6f7fb 35%);
	padding-bottom: 200rpx;
}
.top {
	width: 100%;
	// background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 317%);
	background: linear-gradient(180deg, #bbddfa 90%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 10rpx;
}
.main {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
	gap: 20rpx;
}
.search {
	width: calc(100% - 80rpx);
	height: 76rpx;
	background: #ffffff;
	border-radius: 38rpx 38rpx 38rpx 38rpx;
	border: 2rpx solid #c8dcf1;
	display: flex;
	justify-content: center;
	align-items: center;
	.search-content {
		width: calc(100% - 70rpx);
		display: flex;
		align-items: center;
		gap: 20rpx;
		image {
			width: 30.63rpx;
			height: 30.85rpx;
		}
	}
}
.content {
	width: calc(100% - 80rpx);
	height: 420rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.content-top {
		margin-top: 24rpx;
		width: calc(100% - 40rpx);
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		.content-top-box {
			width: calc(100% - 147rpx);
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			.triangle {
				width: 0;
				height: 0;
				border-left: 14rpx solid transparent;
				border-right: 14rpx solid transparent;
				border-top: 14rpx solid #8590a7;
				position: relative;
			}
			image {
				width: 9.95rpx;
				height: 16.17rpx;
			}
		}
		.btn {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 10rpx;
			text {
				font-weight: bold;
				font-size: 22rpx;
				color: #8590a7;
			}

			image {
				width: 9.95rpx;
				height: 16.17rpx;
			}
		}
	}
	.content-body {
		margin-top: 16rpx;
		width: calc(100% - 40rpx);
		height: 280rpx;
		background: #dddddd;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}
	.content-foot {
		margin-top: 12rpx;
		width: calc(100% - 40rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 12rpx;
		font-weight: 400;
		font-size: 22rpx;
		color: #8590a7;
		image {
			width: 23.43rpx;
			height: 23.43rpx;
		}
	}
}
.clamp {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	font-weight: bold;
	font-size: 30rpx;
	color: #1f2638;
	width: calc(100% - 51rpx);
}
</style>
