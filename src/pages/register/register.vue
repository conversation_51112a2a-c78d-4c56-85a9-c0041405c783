<template>
	<view class="container">
		<u-navbar
			title=" "
			bgColor="rgba(255,255,255,0)"
			:autoBack="true"
			placeholder></u-navbar>
		<image
			class="image"
			:src="imagebaseurl + 'static/icon/register_icon.png'"
			mode=""></image>
		<view class="card">
			<view class="card-content-box">
				<text> <text style="color: red">*</text> 昵称</text>
				<view class="input-box">
					<u--input
						placeholder="请输入用户名"
						border="none"
						v-model="name"
						@change="btnBClick"></u--input>
				</view>
			</view>
			<view class="card-content-box-radion">
				<text>学习形式</text>
				<view class="input-box-radion">
					<view
						class="radion-box"
						@click="seleccMasytype(1)">
						<image
							v-if="radiovalue == 1"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							v-if="radiovalue == 2"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
						<text :class="radiovalue == 1 ? 'text-active' : 'text'">学硕</text>
					</view>
					<view
						class="radion-box"
						style="margin-left: 30rpx"
						@click="seleccMasytype(2)">
						<image
							v-if="radiovalue == 2"
							:src="imagebaseurl + 'static/icon/radion-select.png'"
							mode=""></image>
						<image
							v-if="radiovalue == 1"
							:src="imagebaseurl + 'static/icon/radion-noselect.png'"
							mode=""></image>
						<text :class="radiovalue == 2 ? 'text-active' : 'text'">专硕</text>
					</view>
				</view>
			</view>
			<view class="card-content-box">
				<text>报考分类</text>
				<view
					class="input-box"
					@click="openPicker(0)">
					<text>{{ selectSchoolCategory.classificationName || "请选择报考分类" }}</text>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
			</view>
			<view class="card-content-box">
				<text>报考院校</text>
				<view
					class="input-box"
					@click="openPicker(1)">
					<text>{{ selectedSchool.school || "请选择报考院校" }}</text>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
			</view>
			<view class="card-content-box">
				<text>报考院系</text>
				<view
					class="input-box"
					@click="openPicker(2)">
					<text>{{ selectedSchoolDepartment.schoolDepartment || "请选择报考院系" }}</text>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
			</view>
			<view class="card-content-box">
				<text>拟报考专业</text>
				<view
					class="input-box"
					@click="openPicker(3)">
					<text>{{ selectedMajor.schoolProfession || "请选择拟报考专业" }}</text>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
			</view>

			<button
				open-type="getPhoneNumber"
				@getphonenumber="decryptPhoneNumber"
				><text>确定</text></button
			>
		</view>
		<view v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				:keyName="keyName"
				:defaultIndex="[defaultIndex]"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
	</view>
</template>

<script>
import {
	getSchoolList,
	getProfessionList,
	checkNickName,
	uploadFile,
	requestOpenId,
	requestEncryptedData,
	register,
	getSchoolCategoryList,
	querySchoolList,
	querySchoolProfessionList,
	querySchoolDepartmentList,
} from "../../api/api.js";
import { imagebaseurl } from "../../api/index.js";
export default {
	data() {
		return {
			name: "",
			checkNickName: true,
			radiovalue: 1,
			fileList1: [],
			columns: [[]],
			show: false,
			prickerBoolean: true,
			keyName: "school",
			openId: "",
			sessionKey: "",
			phone: "17373981603",
			imagebaseurl: imagebaseurl,
			// 分类
			schoolCategory: [],
			selectSchoolCategory: {
				classificationName: "全部",
				id: -1,
			},
			selectIndex: 0,
			//学校
			schools: [],
			selectedSchool: {},
			//院系
			schoolDepartmentList: [],
			selectedSchoolDepartment: {},
			//专业
			schoolProfessionList: [],
			selectedMajor: {},
			defaultIndex: 0,
		};
	},
	created() {
		this.getSchoolCategory();
		this.requestOpenIdResp();
		this.getSchool();
	},
	methods: {
		seleccMasytype(index) {
			this.radiovalue = index;
			this.selectSchoolCategory = {};
			this.selectedSchool = {};
			this.selectedSchoolDepartment = {};
			this.selectedMajor = {};
			this.getSchoolCategory();
			this.getSchool();
		},
		decryptPhoneNumber(event) {
			if (this.name == "") {
				this.$showToast("请输入昵称");
				return;
			}
			if (Object.keys(this.selectedSchool).length == 0) {
				this.$showToast("请选择报考院校");
				return;
			}
			if (Object.keys(this.selectedSchoolDepartment).length == 0) {
				this.$showToast("请选择报考院系");
				return;
			}
			if (Object.keys(this.selectedMajor).length == 0) {
				this.$showToast("请选择拟报专业");
				return;
			}
			this.requestEncryptedDataResp(event.detail.encryptedData, event.detail.iv);
		},
		// 请求解密手机号
		requestEncryptedDataResp(encryptedData, iv) {
			requestEncryptedData({
				sessionKey: this.sessionKey,
				encryptedData: encryptedData,
				iv: iv,
			})
				.then(res => {
					if (res.result == "1") {
						let data = JSON.parse(res.data);
						this.phone = data.phoneNumber;
						this.submitRegister();
					} else {
						this.$showToast(res.message || "授权失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "授权失败，请重试");
				});
		},
		submitRegister() {
			register({
				phone: this.phone,
				openId: this.openId,
				nickName: this.name,
				schoolId: this.selectedSchool.id,
				schoolProfessionId: this.selectedMajor.id,
				masterType: this.radiovalue,
			})
				.then(res => {
					console.log(res);
					if (res.result == "1") {
						this.$showToast("注册成功");
						uni.setStorageSync("openId", this.openId);
						uni.setStorageSync("user", res.data);
						uni.setStorageSync("token", res.data.token);
						uni.reLaunch({
							url: "/pages/home/<USER>",
						});
					} else {
						this.$showToast(res.message || "注册失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(res.message || "注册失败，请重试");
				});
		},
		btnBClick() {
			// 此处用法为在js中调用，需要写uni.$u.debounce()
			uni.$u.debounce(this.toNext, 1000);
		},
		toNext() {
			console.log("btnBClick");
			checkNickName({ nickName: this.name, type: 3 }).then(res => {
				console.log(res);
				this.checkNickName = res.data;
				if (!this.checkNickName) {
					this.$showToast("昵称已存在");
				}
			});
		},
		//专硕1，学硕2
		groupChange(e) {
			this.getSchoolCategory();
		},
		openPicker(index) {
			this.selectIndex = index;
			if (index == 0) {
				if (Object.keys(this.schoolCategory).length > 0) {
					this.defaultIndex = this.schoolCategory.findIndex(item => item.id == this.selectSchoolCategory.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex++;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = [];
				this.columns[0].push({
					classificationName: "全部",
					id: -1,
				});
				this.columns[0] = [...this.columns[0], ...this.schoolCategory];
				this.keyName = "classificationName";
			}
			if (index == 1) {
				if (this.schools.length == 0) {
					this.$showToast("暂无更多学校");
					return;
				}
				if (Object.keys(this.selectedSchool).length > 0) {
					this.defaultIndex = this.schools.findIndex(item => item.id == this.selectedSchool.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schools;
				this.keyName = "school";
			}
			if (index == 2) {
				if (Object.keys(this.selectedSchool).length == 0) {
					this.$showToast("请先选择学校");
					return;
				}

				if (Object.keys(this.selectedSchoolDepartment).length > 0) {
					this.defaultIndex = this.schoolDepartmentList.findIndex(item => item.id == this.selectedSchoolDepartment.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schoolDepartmentList;
				this.keyName = "schoolDepartment";
			}
			if (index == 3) {
				if (Object.keys(this.selectedSchoolDepartment).length == 0) {
					this.$showToast("请先选择院系");
					return;
				}
				if (Object.keys(this.selectedMajor).length > 0) {
					this.defaultIndex = this.schoolProfessionList.findIndex(item => item.id == this.selectedMajor.id);
					this.defaultIndex < 0 ? (this.defaultIndex = 0) : this.defaultIndex;
				} else {
					this.defaultIndex = 0;
				}
				this.columns[0] = this.schoolProfessionList;
				this.keyName = "schoolProfession";
			}
			this.show = true;
		},
		confirm(e) {
			if (this.selectIndex == 0) {
				this.selectSchoolCategory = e.value[0];
				this.selectedSchool = {};
				this.selectedSchoolDepartment = {};
				this.selectedMajor = {};
				this.getSchool();
			}
			if (this.selectIndex == 1) {
				this.selectedSchool = e.value[0];
				this.selectedSchoolDepartment = {};
				this.selectedMajor = {};
				this.querySchoolDepartmentListResp();
			}
			if (this.selectIndex == 2) {
				this.selectedSchoolDepartment = e.value[0];
				this.selectedMajor = {};
				this.querySchoolProfessionListResp();
			}
			if (this.selectIndex == 3) {
				this.selectedMajor = e.value[0];
			}
			this.show = false;
			// console.log(e);
			// if (this.prickerBoolean) {
			// 	this.selectedSchool = e.value[0];
			// 	getProfessionList({ schoolId: this.selectedSchool.id, keyWord: "" }).then(res => {
			// 		this.majors = res.data;
			// 		this.show = false;
			// 	});
			// } else {
			// 	this.selectedMajor = e.value[0];
			// 	this.show = false;
			// }
		},
		//学校分类
		getSchoolCategory() {
			getSchoolCategoryList({ masterType: this.radiovalue })
				.then(res => {
					if (res.result == "1") {
						this.schoolCategory = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		//学校
		getSchool() {
			let params = { masterType: this.radiovalue, isOpen: 1 };
			if (Object.keys(this.selectSchoolCategory).length > 0 && this.selectSchoolCategory.id != -1) {
				params.classificationId = this.selectSchoolCategory.id;
			}
			querySchoolList(params)
				.then(res => {
					if (res.result == "1") {
						this.schools = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 查询院系
		querySchoolDepartmentListResp() {
			querySchoolDepartmentList({
				schoolId: this.selectedSchool.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.schoolDepartmentList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 查询专业
		querySchoolProfessionListResp() {
			querySchoolProfessionList({
				schoolDepartmentId: this.selectedSchoolDepartment.id,
			})
				.then(res => {
					if (res.result == "1") {
						this.schoolProfessionList = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast("查询失败，请重试");
				});
		},
		// 获取openid
		requestOpenIdResp() {
			let _that = this;
			uni.login({
				provider: "weixin",
				success: function (event) {
					const { code } = event;
					console.log("event------------event");
					//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
					requestOpenId({
						code: code,
					})
						.then(res => {
							if (res.result == "1") {
								_that.openId = res.data.openid;
								_that.sessionKey = res.data.sessionKey;
								_that.$store.commit("updateOpenId", _that.openId);
							}
						})
						.catch(err => {
							_that.$showToast(err.message || "授权失败，请重试");
						});
				},
				fail: function (err) {
					// 登录授权失败
					_that.$showToast(err.message || "授权失败，请重试");
				},
			});
		},
	},
};
</script>

<style lang="scss">
.container {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.image {
	position: absolute;
	top: 0;
	width: 100%;
	height: 522rpx;
}
.card {
	width: 100%;
	height: 1190rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	border: 2rpx solid #ffffff;
	z-index: 99;
	position: relative;
	top: 275rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.card-content-box {
	margin-top: 40rpx;
	width: calc(100% - 68rpx);
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #646d80;
	}

	.input-box {
		padding-left: 22rpx;
		width: calc(100% - 22rpx);
		height: 70rpx;
		background: #f5f7fa;
		border-radius: 10rpx 10rpx 10rpx 10rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #1f2638;
		}
		image {
			width: 10.65rpx;
			height: 18.63rpx;
			margin-right: 30rpx;
		}
	}
}
::v-deep .u-radio-group {
	flex: 0 !important;
}
.card-content-box-radion {
	margin-top: 40rpx;
	width: calc(100% - 68rpx);
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #646d80;
	}
	.input-box-radion {
		width: calc(100% - 22rpx);
		height: 70rpx;
		background: white;
		border-radius: 10rpx 10rpx 10rpx 10rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.radion-box {
			width: 200rpx;
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			.text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #8590a7;
			}
			.text-active {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #1f2638;
			}

			image {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}
}
.avatar-upload {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 20px;
}
.avatar {
	width: 100px;
	height: 100px;
	border-radius: 50%;
	margin-bottom: 10px;
}
.form-item {
	margin-bottom: 20px;
	width: calc(100% - 100rpx);
	display: flex;
	justify-content: center;
	align-items: baseline;
	gap: 10rpx;
	.text {
		width: 180rpx;
	}
}
.picker {
	border: 1px solid #ccc;
}
button {
	// margin-top: 252rpx;
	margin-top: 50rpx;
	width: calc(100% - 100rpx);
	height: 98rpx;
	background: #2168fe;
	box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
	border-radius: 50rpx 50rpx 50rpx 50rpx;
	color: #ffffff;
}
</style>
