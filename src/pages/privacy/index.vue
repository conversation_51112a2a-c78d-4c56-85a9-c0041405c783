<template>
  <view class="privacy">
    <view class="content">
      <view class="title">隐私保护指引</view>
      <view class="des">
        在使用当前小程序服务之前，请仔细阅读
        <text class="link" @click="openPrivacyContract">{{ privacyContractName }}</text>
        。
        <view>如你同意{{ privacyContractName }}，请点击“同意”开始使用。</view>
      </view>
      <view class="btns">
        <button class="item reject" @click="handleDisagreePrivacyAuthorization">拒绝</button>
        <button
          id="agree-btn"
          class="item agree"
          open-type="agreePrivacyAuthorization"
          @agreeprivacyauthorization="handleAgreePrivacyAuthorization"
        >
          同意
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import Vue from 'vue';
export default {
  name: 'privacy-popup',
  data() {
    return {
      privacyContractName: '《隐私保护指引》', // 小程序协议名称
    };
  },
  created() {
    wx.getPrivacySetting({
      success: (res) => {
        if (res.errMsg == 'getPrivacySetting:ok') {
          this.privacyContractName = res.privacyContractName; // 更新小程序协议名称
        }
      },
    });
  },
  methods: {
    // 打开隐私协议页面
    openPrivacyContract() {
      wx.openPrivacyContract({});
    },
    // 拒绝隐私协议
    handleDisagreePrivacyAuthorization() {
      this.$resolvePrivacyAuthorization({ event: 'disagree' });
      uni.navigateBack();
    },
    // 同意隐私协议
    handleAgreePrivacyAuthorization() {
      this.$resolvePrivacyAuthorization({ buttonId: 'agree-btn', event: 'agree' });
      uni.navigateBack();
    },
  },
};
</script>
<style scoped>
.privacy {
  height: 100vh;
  width: 100vw;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.content {
  width: 700rpx;
  padding: 48rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
}

.content .title {
  text-align: center;
  color: #333;
  font-weight: bold;
  font-size: 32rpx;
}

.content .des {
  font-size: 26rpx;
  color: #666;
  margin-top: 40rpx;
  text-align: justify;
  line-height: 1.6;
}

.content .des .link {
  color: #07c160;
  text-decoration: underline;
}

.btns {
  margin-top: 48rpx;
  display: flex;
  justify-content: space-between;
}

.btns .item {
  justify-content: space-between;
  width: 47%;
  margin: 0;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: #00a851;
}

.btns .item::after {
  border: none;
}

.btns .reject {
  background: #f4f4f5;
  color: #909399;
  border-color: #ddd;
}

.btns .agree {
  background: #07c160;
  color: #fff;
}
</style>
