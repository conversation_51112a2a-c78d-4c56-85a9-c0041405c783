<template>
	<view class="container">
		<u-navbar leftText="关于我们" leftIconColor="#ffffff" autoBack title=" " :bgColor="bgColor" safeAreaInsetTop placeholder :fixed="false"></u-navbar>
		<image class="background-icon" :src="imagebaseurl + 'static/icon/aboutUs_background.png'" mode=""></image>
		<view class="top">
			<text>立格致远教育！</text>
			<text>大家好，下面是立格君对于我们的简单介绍~</text>
		</view>
		<view class="qr-box">
			<view class="qrCode"></view>
			<text>（扫码加立格君获取、答疑、干货）</text>
		</view>
		<view class="main">
			<view class="main-top">
				<text>立格介绍</text>
				<text>立格考研在各个平台都有分享考研的干货以及资讯~</text>
			</view>
			<view class="box">
				<view class="box-top">
					<image :src="imagebaseurl + 'static/icon/weixi.png'" mode=""></image>
					<text>公众号</text>
				</view>
				<view class="box-body">
					<image class="logo" :src="imagebaseurl + 'static/icon/logo.png'" mode=""></image>
					<view class="text-content">
						<text>立格致远教育</text>
						<text>立格君整理发布****篇内容</text>
					</view>
					<image class="btn" :src="imagebaseurl + 'static/icon/arrow_right_icon.png'" mode=""></image>
				</view>
			</view>
			<view class="box">
				<view class="box-top">
					<image :src="imagebaseurl + 'static/icon/xiaohongshu.png'" mode=""></image>
					<text>小红书</text>
				</view>
				<text class="text">
					这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符
				</text>
				<view class="qr-box"></view>
			</view>
			<view class="box">
				<view class="box-top">
					<image :src="imagebaseurl + 'static/icon/shipinghao.png'" mode=""></image>
					<text>视频号</text>
				</view>
				<text class="text">
					这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符
				</text>
				<view class="qr-box"></view>
			</view>
			<view class="box">
				<view class="box-top">
					<image :src="imagebaseurl + 'static/icon/douyin.png'" mode=""></image>
					<text>抖音号</text>
				</view>
				<text class="text">
					这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符
				</text>
				<view class="qr-box"></view>
			</view>
			<view class="box">
				<view class="box-top">
					<image :src="imagebaseurl + 'static/icon/bilbil.png'" mode=""></image>
					<text>B站</text>
				</view>
				<text class="text">
					这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符这是一段占位符
				</text>
				<view class="qr-box"></view>
			</view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from '../../api/index.js';
export default {
	data() {
		return {
			bgColor: 'rgba(255,255,255,0)',
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			iconUrl: require('../../static/icon/courses_icon_4.png')
		};
	},
	mounted() {
		// 打印小程序场景值
		console.log('小程序场景值：', uni.getLaunchOptionsSync().scene);
		this.getElementHeight();
	},
	methods: {
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select('.top')
				.boundingClientRect((data) => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log('元素的高度：', data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		}
	}
};
</script>

<style lang="scss">
::v-deep .u-navbar {
	width: 100% !important;
}

::v-deep .u-navbar__content__left__text {
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 36rpx;
	color: #ffffff;
}
::v-deep .u-navbar__content__left.data-v-75dad532,
.u-navbar__content__right {
	padding: 0 10rpx !important;
}
::v-deep .u-cell__title-text {
	margin-left: 10rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 28rpx;
	color: #1f2638;
}
.background-icon {
	position: absolute;
	width: 100%;
	height: 1082rpx;
	z-index: -1;
}
.container {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	// background: #3480fa;
	padding-bottom: 100rpx;
}
.top {
	margin-top: 28rpx;
	width: calc(100% - 50rpx);
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	text:nth-child(1) {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 36rpx;
		color: #ffffff;
		line-height: 70rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	text:nth-child(2) {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 28rpx;
		color: #ffffff;
	}
}
.qr-box {
	margin-top: 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	.qrCode {
		width: 254rpx;
		height: 236rpx;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		background: whitesmoke;
	}
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 20rpx;
		color: #ffffff;
	}
}
.main {
	margin-top: 40rpx;
	width: calc(100% - 60rpx);
	height: 3040rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.main-top {
		margin-top: 30rpx;
		width: calc(100% - 52rpx);
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		text:nth-child(1) {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #1f4085;
		}
		text:nth-child(2) {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: rgba(31, 64, 133, 0.5);
		}
	}
	.box {
		margin-top: 30rpx;
		width: calc(100% - 15rpx);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
		.box-top {
			width: calc(100% - 25rpx);
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 10rpx;
			image {
				width: 34.38rpx;
				height: 28rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
		}
		.box-body {
			width: calc(100% - 25rpx);
			height: 160rpx;
			background: #ffffff;
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			border: 2rpx solid #f5f5f5;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 60rpx;

			.logo {
				width: 108rpx;
				height: 108rpx;
			}
			.text-content {
				display: flex;
				flex-direction: column;
				gap: 10rpx;
				text:nth-child(1) {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #1f2638;
				}
				text:nth-child(2) {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #8590a7;
				}
			}
			.btn {
				width: 10.65rpx;
				height: 18.63rpx;
			}
		}
		.text {
			width: calc(100% - 25rpx);
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #8590a7;
		}
		.qr-box {
			width: 330rpx;
			height: 316rpx;
			background: #ffffff;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			border: 2rpx solid #707070;
		}
	}
}
</style>