<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-12-09 23:23:26
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2023-02-19 21:57:21
 * @FilePath: /law_train_miniapp/src/pages/score/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <view class="content-view">
        <van-popup :show="showYear" :round="true" position="bottom">
                <van-picker  
                    show-toolbar
                    item-height="30"
                    :columns="yearList"
                    @confirm="onConfirm"
                    @cancel="onCancel"
                />
            </van-popup>
        <view class="flex-row">
            <view class="flex-row screen-view" @tap="showPopup">
                {{yearInfo}}年
                <van-icon :name="iconName" />
            </view>
        </view>

        <view class="score-view">
            <view class="title-view">
                <view class="subject-label" :style="{color: '#fff'}">科目</view>
                <view class="score-label" :style="{color: '#fff'}">成绩</view>
            </view>
            <block v-for="item, index in scoreList" :key="index">
                <view class="title-view" :style="{backgroundColor: index % 2 == 0 ? 'rgba(205,190,255,0.3)' : '#fff', fontWeight: index == scoreList.length - 1 ? 'bold' : 'normal'}" >
                    <view class="subject-label">{{item.name || '--'}}</view>
                    <view class="score-label">{{item.score || '--'}}</view>
                </view>
            </block>
        </view>

        <view class="cell-view">
            <view class="flex-row cell-title-view">
                <label style="font-size: 40rpx; font-weight: bold">排名</label>
                <label style="font-size: 26rpx">学生总数：{{courseData.totalUserCount}}个</label>
            </view>
            <label style="font-size: 30rpx; font-weight: bold">在{{courseData.schoolInfoStr}}中</label>
            <view class="flex-row" style="margin-top: 20rpx; border-radius: 10rpx; overflow: hidden;">
                <view class="flex-col item-view">
                    <label style="font-size: 60rpx; font-weight: bold; color: #835FFF;">{{courseData.maxUserTotalScore || '--'}}</label>
                    <label style="font-size: 30rpx; font-weight: bold">当前最高分</label>
                </view>
                <view class="item-line" />
                <view class="flex-col item-view">
                    <label style="font-size: 60rpx; font-weight: bold; color: #835FFF;">{{courseData.totalScoreRank || '--'}}</label>
                    <label style="font-size: 30rpx; font-weight: bold">当前排名</label>
                </view>
                <view class="item-line" />
                <view class="flex-col item-view">
                    <label style="font-size: 60rpx; font-weight: bold; color: #835FFF;">{{courseData.totalScorePercent || '--'}}%</label>
                    <label style="font-size: 30rpx; font-weight: bold">超越其他考生</label>
                </view>
            </view>
        </view>

        <view style="margin-top: 30rpx;">
            <label style="font-size: 40rpx; font-weight: bold">单科成绩排行</label>
            <view class="flex-row single-view" v-for="item, index in scoreList" :key="index" style="margin-top: 20rpx;" v-if="(index<scoreList.length - 1)">
                <view class="flex-row row-view" :style="{backgroundColor: index % 2 == 0 ? 'rgba(205,190,255,0.3)' : '#fff', fontWeight: index == scoreList.length - 1 ? 'bold' : 'normal'}" >
                    <view class="flex-col item-view2">
                        <label style="font-size: 40rpx; font-weight: bold; color: #835FFF;">{{item.score}}</label>
                        <label style="font-size: 28rpx; font-weight: bold">{{item.name || '--'}}</label>
                    </view>
                    <view class="flex-col item-view2">
                        <label style="font-size: 40rpx; font-weight: bold; color: #835FFF;">{{item.rank || '--'}}</label>
                        <label style="font-size: 28rpx; font-weight: bold">当前排名</label>
                    </view>
                    <view class="flex-col item-view2">
                        <label style="font-size: 40rpx; font-weight: bold; color: #835FFF;">{{item.percent || '--'}}%</label>
                        <label style="font-size: 28rpx; font-weight: bold">超越其他考生</label>
                    </view>
                </view>
            </view>
        </view>

        <view style="margin-top: 30rpx;" v-if="courseData.wechatGroupUrl">
            <view class="qr-box">
                <image class="qr-box-img" :src="courseData.wechatGroupUrl" @click="previewImage(courseData.wechatGroupUrl)" /> 
                <view style="margin-top: 30rpx;">{{courseData.wechatGroupName || '--'}}</view>
            </view>	
        </view>

        <view class="page-section-spacing" style="margin-top: 30rpx;">
            <label style="font-size: 40rpx; font-weight: bold;margin-top: 20rpx;">更多资讯，欢迎扫码关注</label>
            <swiper class="swiper" 
                style="margin-top: 20rpx;"
                indicator-dots="true" 
                autoplay="true" 
                interval="5000" 
                duration="1500"	>
                <swiper-item v-for="(item , index) in homeSlide" :key="index">
                    <image :src="item.imageUrl" mode="aspectFill" @click="previewImage(item.imageUrl)"></image>
                </swiper-item>
            </swiper>
        </view>

        <view style="margin-top: 30rpx;" v-if="courseData.teacherName">
            <label style="font-size: 40rpx; font-weight: bold">录取评估咨询</label>
            <view style="margin-top: 20rpx;display: flex;" class="bottom">
                <view>
                    <van-image
                        width="60"
                        height="60"
                        :src="courseData.teacherWechatUrl"
                        @click="previewImage(courseData.teacherWechatUrl)"
                    />
                </view>
                <view style="display:flex;flex-direction: column; align-items: left; justify-content: center;margin-left: 20rpx;">
                    <view>{{courseData.teacherName || '--'}}</view>
                    <view>{{courseData.teacherPhone  || '--'}}</view>
                </view>
            </view>	
        </view>
        
        <view class="tip-view">
            <van-icon name="warning-o" color="red" />
            <span>免责声明：以下内容依据参与查排名活动同学填写的数据。内容仅供参考，不作为实际录取依据</span>
        </view>

        <view class="foot-view">
            <!-- <span>邀请好友，一起查排名</span> -->
            <button class="submit-btn" type="primary" open-type="share">分享好友</button>
        </view>
    </view>
</template>

<script>
import { requestOpenId, queryUserScore,queryBannerList } from '../../api/api'
import uQRCode from '../../utils/uqrcode.js' //引入uqrcode.js
export default {
    data() {
        return {
            yearInfo:'',
            showYear: false,
            iconName:'arrow-down',
            openId:'',
            yearList: [],
            courseList:[],
            scoreList: [],
            course1:{},
            course2:{},
            course3:{},
            course4:{},
            course5:{},
            courseData:{},
            qrShow: false,
            homeSlide:[],
        }
    },

    onLoad(query) {
        let openId = this.$store.state.openId;
        let scoreList = this.$store.state.scoreList;

        this.openId = openId;
        if (this.openId) {
            if (scoreList.length == 0) {
                this.queryUserScoreResp()
            }else{
                this.dealIndexPage(scoreList)
            }
        }else{
            uni.reLaunch({
                url: "/pages/index/index"
            })
        }
        
        this.requestBanner()

        wx.showShareMenu({
            withShareTicket: true,
            menus: ["shareAppMessage", "shareTimeline"],// 发送朋友，发送朋友圈
        });
    },
    watch: {
        showYear(newValue,oldValue){
            if (newValue) {
                this.iconName = 'arrow-up'
            }else{
                this.iconName = 'arrow-down'
            }
        }
    },
    onShareAppMessage() {
        return {
            title: `立格教育`,
            imageUrl: "https://qnpb.ligeedu.cn/miniapp_share.jpg",
            success: function (res) {
                console.log("success22:" + JSON.stringify(res));
            },
            fail: function (err) {
                console.log("fail22:" + JSON.stringify(err));
            },
        };
    },
    methods: {
        requestBanner(){
            queryBannerList({
                
            }).then(res => {
                if (res.result == '1') {
                    this.homeSlide = res.data
                }
            }).catch(err => {

            })
        },
        onConfirm(value, index) {
            this.showYear = false;
            console.log(value.detail.value)
            this.dealDataInfo(value.detail.index)
        },
        onCancel() {
            this.showYear = false;
        },
        showPopup() {
            this.showYear = true;
        },
        
        // 查询用户成绩历史
        queryUserScoreResp() {
            queryUserScore({
                openId: this.openId
            }).then(res => {
                if (res.result == '1') {
                    this.dealIndexPage(res.data)
                }
            }).catch(err => {

            })
        },

        dealIndexPage(list){
            if (list.length > 0) {
                list.forEach(element => {
                    this.yearList.push(element.year)
                });
                this.courseList = list
                this.dealDataInfo(0)
            }else{
                uni.reLaunch({
                    url: "/pages/index/index"
                })
            }
        },

        dealDataInfo(index){
            this.courseData = this.courseList[index]
            this.courseData.schoolInfoStr = this.courseData.school+'/'+this.courseData.schoolDepartment+'/'+this.courseData.schoolProfession
            this.yearInfo = this.courseData.year

            this.course1.name = this.courseData.course1Name
            this.course1.score = this.courseData.course1Score
            this.course1.rank = this.courseData.course1Rank
            this.course1.percent = this.courseData.course1Percent

            this.course2.name = this.courseData.course2Name
            this.course2.score = this.courseData.course2Score
            this.course2.rank = this.courseData.course2Rank
            this.course2.percent = this.courseData.course2Percent

            this.course3.name = this.courseData.course3Name
            this.course3.score = this.courseData.course3Score
            this.course3.rank = this.courseData.course3Rank
            this.course3.percent = this.courseData.course3Percent

            this.course4.name = this.courseData.course4Name
            this.course4.score = this.courseData.course4Score
            this.course4.rank = this.courseData.course4Rank
            this.course4.percent = this.courseData.course4Percent

            this.course5.name = '总分'
            this.course5.score = this.courseData.totalScore

            this.scoreList = []
            this.scoreList.push(this.course1)
            this.scoreList.push(this.course2)
            this.scoreList.push(this.course3)
            this.scoreList.push(this.course4)
            this.scoreList.push(this.course5)

            // this.qrFun(this.courseData.wechatGroupUrl)
        },
        // //**生成二维码**//
        // qrFun: function(text) {
        //     this.qrShow = true
        //     uQRCode.make({
        //         canvasId: 'qrcode',
        //         componentInstance: this,
        //         text: text,
        //         size: 150,
        //         margin: 0,
        //         backgroundColor: '#ffffff',
        //         foregroundColor: '#000000',
        //         fileType: 'jpg',
        //         errorCorrectLevel: uQRCode.errorCorrectLevel.H,
        //         success: res => {}
        //     })
        // }

        previewImage(image) {
            //预览图片
            uni.previewImage({
                urls: [image]
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.content-view {
    width: 100vw;
    height: 100vh;
    // background-color: rgba($color: #CDBEFF, $alpha: 0.10);
    background-color: #fff;
    overflow: scroll;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    padding: 30rpx;

    .screen-view {
        width: 80%;
        justify-content: space-between;
        margin-left: 10%;
    }
    .score-view {
        margin-top: 30rpx;
        background-color: #fff;
        border-radius: 10rpx;
        overflow: hidden;
        font-size: 28rpx;
        .title-view {
            background-color: #835FFF;
            display: flex;
            flex-direction: row;
        }
        .subject-label {
            width: calc(100% - 200rpx);
            height: 60rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
        .score-label {
            width: 200rpx;
            height: 60rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
    }
    .cell-view {
        background-color: rgba($color: #CDBEFF, $alpha: 0.30);
        margin-top: 30rpx;
        box-sizing: border-box;
        padding: 20rpx;
        .cell-title-view {
            justify-content: space-between;
        }
        .item-view {
            width: 33%;
            height: 200rpx;
            background-color: #fff;
            justify-content: center;
            align-items: center;
        }
        .item-line {
            width: 1rpx;
            height: 40rpx;
        }
    }

    .qr-box {
		width: 400rpx;
		height: 400rpx;
		margin: 0 auto;
		margin-top: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .qr-box-img {
            width: 300rpx;
            height: 300rpx;
        }
	}

    .swiper{
        height: 300rpx;
    }
    // swiper-item 里面的图片高度
    swiper-item image{
        width: 100%;
        height: 300rpx;
    }

    .single-view {
        background-color: rgba($color: #CDBEFF, $alpha: 0.30);
        .row-view {
            width: 100%;
        }
        .item-view {
            width: 33%;
            height: 200rpx;
            justify-content: center;
            align-items: center;
        }

        .item-view2 {
            width: 33%;
            height: 120rpx;
            justify-content: center;
            align-items: center;
        }
    }
}
.tip-view {
    margin-top: 30rpx;
    background-color: #f1f1f1;
    font-size: 30rpx;
    padding: 20rpx;
}
.foot-view {
    
    .submit-btn {
        margin-top: 80rpx;
        width: 80vw;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        background-color: #835FFF;
    }
}

</style>