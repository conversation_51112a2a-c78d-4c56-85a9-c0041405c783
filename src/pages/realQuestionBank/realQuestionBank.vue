<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="真题库"
				title=" "
				leftIconSize="25"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder>
				<view
					class="u-nav-slot"
					slot="left">
					<u-icon
						name="arrow-left"
						size="25"
						color="black"
						@click="goBack"></u-icon>
					<text
						class="top-title"
						@click="goBack"
						>真题库</text
					>
					<!-- 	<view class="top-select" @click="show = true">
						<text>陕西省</text>
						<u-icon name="arrow-down" size="10" color="#FFFFFF"></u-icon>
					</view> -->
				</view>
			</u-navbar>
			<view class="top-foot">
				<view class="top-foot-left">
					<image
						:src="imagebaseurl + 'static/icon/subject_icon_2.png'"
						mode=""></image>
					<text>院校</text>
				</view>
				<view class="top-foot-right">
					<image
						:src="imagebaseurl + 'static/icon/book_icon.png'"
						mode=""></image>
					<text>专业/真题</text>
				</view>
			</view>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<scroll-view
				scroll-y="true"
				class="scroll-L"
				:style="'height:' + 'calc(100vh - ' + topHeight + 'px)'">
				<view
					class="leftMenu"
					v-for="(item, index) in schoolList"
					:key="index">
					<!-- 一级菜单 -->
					<view
						:class="selectLeftItem.schoolId == item.schoolId ? 'leftMenu-title-active' : 'leftMenu-title'"
						@click="openLeftMenu(item, index)">
						<image
							:style="selectLeftItem.schoolId == item.schoolId ? 'transform: rotate(90deg);' : ''"
							:src="imagebaseurl + 'static/icon/arrow_right_bold.png'"
							mode=""></image>

						<text>{{ item.schoolName }}</text>
						<!-- <u-icon :name="selectLeftItem.schoolId == item.schoolId ? 'arrow-up' : 'arrow-down'" size="14"></u-icon> -->
					</view>
					<!-- 二级菜单 -->
					<view
						v-if="selectLeftItem.schoolId == item.schoolId"
						class="leftMenu-box"
						v-for="(subItem, subIndex) in questionList"
						:key="subItem.id">
						<view
							:class="selectSubLeftItem.courseId == subItem.courseId ? 'leftMenu-subtitle-active leftMenu-subtitle ' : 'leftMenu-subtitle'"
							@click="openSubLeftMenu(subItem, subIndex)">
							<text class="title">{{ subItem.courseName }}</text>
							<u-icon
								:name="selectSubLeftItem.courseId == subItem.courseId ? 'arrow-up' : 'arrow-down'"
								size="14"></u-icon>
						</view>
						<!-- 三级菜单 -->
						<view
							class="leftMenu-box"
							v-if="selectSubLeftItem.courseId == subItem.courseId"
							:style="selectSubLeftItem.courseId == subItem.courseId ? 'height:' + questionListChildrenHeight + 'rpx' : ''">
							<view
								:class="oldChildenItem.professionId == item3.professionId ? 'leftMenu-content active' : 'leftMenu-content'"
								v-for="(item3, index3) in questionListChildren"
								:key="index3"
								@click="openLeftMenuChilden(item3, index3)">
								<text>{{ item3.professionName }}</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<scroll-view
				scroll-y="true"
				class="scroll-R"
				:style="'height:' + 'calc(100vh - ' + topHeight + 'px)'">
				<view
					class="rightMenu"
					v-for="(item, index) in questionListChildren"
					:key="index">
					<view
						:class="selectRighttItem.professionId == item.professionId ? 'rightMenu-title-active' : 'rightMenu-title'"
						@click="openRightMenu(item)">
						<view class="rightMenu-title-right">
							<image
								:src="imagebaseurl + 'static/icon/arrow_right_bold.png'"
								mode=""></image>
							<text>{{ item.professionName }}</text>
						</view>
						<!-- <u-icon :name="selectRighttItem == item.id ? 'arrow-up' : 'arrow-down'" size="16.17rpx"></u-icon> -->
					</view>
					<view
						class="rightMenu-box"
						:style="selectRighttItem.professionId == item.professionId ? 'height:' + questionListChildrenTopicHeight + 'rpx' : ''">
						<view
							:class="oldChildenRightItem.customizeId == item2.customizeId ? 'rightMenu-content active' : 'rightMenu-content'"
							v-for="(item2, index2) in questionListChildrenTopic"
							:key="index2"
							@click="openRightMenuChilden(item2)">
							<view class="right-content-l">
								<text>{{ item2.customizeName }}</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<u-picker
			:show="show"
			:columns="columns"
			confirmColor="#2168fe"
			@cancel="show = false"
			@confirm="show = false"></u-picker>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import {
	realSubjectRepositoryGetSchoolList,
	realSubjectRepositoryGetCourseList,
	realSubjectRepositoryGetProfessionList,
	realSubjectRepositoryGetCustomizeList,
} from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			user: uni.getStorageSync("user"),
			topHeight: 0,
			//图片基准地址
			imagebaseurl: imagebaseurl,
			show: false,
			columns: [[]],
			// 一级菜单
			selectLeftItem: {},
			// 二级菜单
			selectSubLeftItem: {},
			// 三级菜单
			oldChildenItem: {},
			// 一级菜单索引
			leftIndex: -1,
			// 二级菜单索引
			leftSubIndex: -1,
			// 一级菜单
			selectRighttItem: -1,
			// 二级菜单
			oldChildenRightItem: {},
			//接口参数
			params: {
				pageNo: 1,
				pageSize: 10,
			},
			schoolList: [],
			//学校真题库
			questionList: [],

			//专业课程
			questionListChildren: [],
			questionListChildrenHeight: 0,
			//专业真题
			questionListChildrenTopic: [],
			questionListChildrenTopicHeight: 0,
			defaultIndex: 0,
		};
	},
	created() {
		this.params.masterType = this.user.masterType;
		realSubjectRepositoryGetSchoolList(this.params)
			.then(res => {
				if (res.result == "1") {
					this.schoolList = res.data;
					this.schoolList = res.data;
					this.selectLeftItem = this.schoolList[0];
					this.params.schoolId = this.selectLeftItem.schoolId;

					realSubjectRepositoryGetCourseList(this.params)
						.then(res => {
							if (res.result == "1") {
								if (res.data.list.length > 0) {
									this.questionList = res.data.list;
									this.selectSubLeftItem = this.questionList[0];
									this.params.courseId = this.selectSubLeftItem.courseId;

									realSubjectRepositoryGetProfessionList(this.params)
										.then(res => {
											if (res.result == "1") {
												this.questionListChildren = res.data.list;
												this.questionListChildrenHeight = this.questionListChildren.length * 80;
											} else {
												this.$showToast(res.message || "查询失败请重试");
											}
										})
										.catch(err => {
											this.$showToast(err.message || "查询失败请重试");
										});
								}
							} else {
								console.error(res);
								this.$showToast(res.message || "查询失败请重试");
							}
						})
						.catch(err => {
							console.error(err);
							this.$showToast(err.message || "查询失败请重试");
						});
				} else {
					this.$showToast(res.message + "查询失败请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message + "查询失败请重试");
			});
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		//获取学校 -真题题库
		getSchoolList() {
			this.params.masterType = this.user.masterType;
			realSubjectRepositoryGetSchoolList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.schoolList = res.data;
						this.selectLeftItem = this.schoolList[0];
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		//查询学校下的课程- -真题题库
		getSchoolTopicList() {
			this.params.masterType = this.user.masterType;
			this.params.schoolId = this.selectLeftItem.schoolId;
			realSubjectRepositoryGetCourseList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.questionList = res.data.list;
						this.questionListChildren = [];
						if (res.data.list.length == 0) {
							this.$showToast("暂无真题数据");
						}
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		//查询课程下的专业 - 真题题库
		getSchoolTopicListChildern() {
			this.params.courseId = this.selectSubLeftItem.courseId;
			realSubjectRepositoryGetProfessionList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.questionListChildren = res.data.list;
						this.questionListChildrenHeight = this.questionListChildren.length * 80;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		//查询课程下的专业的真题 - 真题题库
		getSchoolTopicListChildrenTopic() {
			this.params.professionId = this.oldChildenItem.professionId;
			realSubjectRepositoryGetCustomizeList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.questionListChildrenTopic = res.data.list;
						this.questionListChildrenTopicHeight = this.questionListChildrenTopic.length * 86;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		openLeftMenu(item, index) {
			console.log(item);
			if (this.selectLeftItem.schoolId == item.schoolId) {
				this.selectLeftItem = {};
			} else {
				this.leftIndex = index;
				this.selectLeftItem = item;
				this.getSchoolTopicList();
				// 重置二级菜单选中状态
				this.selectSubLeftItem = {};
			}
		},
		openSubLeftMenu(subItem, index) {
			this.questionListChildrenHeight = 0;
			if (this.selectSubLeftItem.courseId == this.questionList[index].courseId) {
				this.selectSubLeftItem = {};
			} else {
				this.leftSubIndex = index;
				this.selectSubLeftItem = this.questionList[index];
				this.getSchoolTopicListChildern();
			}
		},
		openLeftMenuChilden(item3, index) {
			this.oldChildenItem = item3;
			this.openRightMenu(item3);
		},
		openRightMenuChilden(item2) {
			this.oldChildenRightItem = item2;
			const realQuestionBank = {
				selectLeftItem: this.selectLeftItem,
				selectSubLeftItem: this.selectSubLeftItem,
				selectRighttItem: this.selectRighttItem,
				oldChildenRightItem: this.oldChildenRightItem,
			};
			uni.setStorageSync("realQuestionBank", realQuestionBank);
			uni.navigateTo({
				url: "/pages/realQuestionBank/testQuestions?appSubjectRepositoryId=" + item2.repositoryId,
			});
		},
		openRightMenu(item) {
			this.selectRighttItem = item;
			this.oldChildenItem = item;
			this.getSchoolTopicListChildrenTopic();
		},
		goBack() {
			uni.navigateBack({ delta: 1 });
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-navbar__content__left.data-v-75dad532,
.u-navbar__content__right {
	padding: 0 !important;
}
.u-nav-slot {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 20rpx;
	.top-title {
		font-weight: bold !important;
		font-size: 36rpx !important;
		color: #1f2638 !important;
	}
	.top-select {
		width: 128rpx;
		height: 48rpx;
		background: #94b2d6;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 10rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 24rpx;
		color: #ffffff;
	}
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
	.top-body {
		width: calc(100% - 69rpx);
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		.top-body-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			.historical-records {
				width: 208rpx;
				height: 40rpx;
				background: #ffffff;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: bold;
				font-size: 20rpx;
				color: #2168fe;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 10rpx;
				image {
					margin-top: 4rpx;
					width: 21.5rpx;
					height: 21.5rpx;
				}
			}
		}
		.unlock {
			width: 152rpx;
			height: 72rpx;
		}
	}
	.top-foot {
		// margin-top: 20rpx;
		width: 100%;
		height: 64rpx;
		background: #c1d6f0;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.top-foot-left {
			width: 280rpx;
			height: 64rpx;
			background: #ffffff;
			border-radius: 0rpx 30rpx 0rpx 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			image {
				width: 28.82rpx;
				height: 26.24rpx;
			}
		}
		.top-foot-right {
			width: calc(100% - 280rpx);
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 10rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #1f4085;
			image {
				width: 28.82rpx;
				height: 26.24rpx;
			}
		}
	}
}
.main {
	width: 100%;
	position: fixed;
	display: flex;
	justify-content: flex-start;
	z-index: 9;
}
.scroll-L {
	width: 280rpx;
	background: #ffffff;
}
.scroll-R {
	width: calc(100% - 280rpx);
}
.rightMenu {
	width: calc(100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	.rightMenu-title {
		width: calc(100% - 20rpx);
		margin-left: 20rpx;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: flex-start;

		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #8590a7;
			image {
				width: 12rpx;
				height: 24rpx;
			}
		}
	}
	.rightMenu-title-active {
		width: calc(100% - 20rpx);
		margin-left: 20rpx;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1f2638;
			image {
				width: 12rpx;
				height: 24rpx;
			}
		}
	}
	.rightMenu-box {
		width: calc(100% - 47rpx);
		height: 0;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		gap: 50rpx;
		transition: height 0.5s ease-in-out;
		.rightMenu-content {
			// margin-bottom: 50rpx;
			margin-left: 20rpx;
			width: 220rpx;
			height: 64rpx;
			display: flex;
			align-items: center;

			.right-content-l {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 20rpx;
				font-weight: 500;
				font-size: 28rpx;

				text {
					margin-left: 25rpx;
				}
				.sing {
					padding: 5rpx;
					width: 52rpx;
					height: 28rpx;
					background: #dce7ff;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					text {
						margin-left: 0;
						font-weight: 400;
						font-size: 20rpx;
						color: #2168fe;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
		}

		.active {
			width: 220rpx;
			height: 64rpx;
			background: #2168fe;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
			color: white !important;
			.sing {
				padding: 5rpx;
				width: 52rpx;
				height: 28rpx;
				background: white !important;
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				text {
					margin-left: 0;
					font-weight: 400;
					font-size: 20rpx;
					color: #2168fe;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}
}
.leftMenu {
	width: 280rpx;
	display: flex;
	min-height: 0rpx;
	flex-direction: column;
	align-items: center;
	transition: height 0.5s ease-in-out;
	.leftMenu-title {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		width: 228rpx;
		display: flex;
		// justify-content: space-between;
		justify-content: flex-start;
		gap: 17rpx;
		align-items: center;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #8590a7;
		image {
			width: 12rpx;
			height: 24rpx;
		}
	}
	.leftMenu-title-active {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		width: 228rpx;
		display: flex;
		justify-content: flex-start;
		gap: 17rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #1f2638;
		image {
			width: 12rpx;
			height: 24rpx;
		}
	}

	.leftMenu-box {
		width: 280rpx;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: height 0.5s ease-in-out;
		.leftMenu-content {
			width: 280rpx;
			height: 68rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 500;
			font-size: 28rpx;
			color: #8590a7;
			border-left: 10rpx #ffffff solid;
			text {
				margin-left: -18rpx;
			}
		}
		.active {
			background: rgba(33, 157, 254, 0.1);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			font-weight: bold;
			font-size: 28rpx;
			color: #2168fe;
			border-left: 10rpx #2168fe solid;
		}
	}
}
.leftMenu-subtitle {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
	width: 228rpx;
	display: flex;
	justify-content: flex-end;
	align-items: center;

	image {
		width: 12rpx;
		height: 24rpx;
	}
	.title {
		width: 158rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #8590a7;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
.leftMenu-subtitle-active {
	.title {
		color: #1f2638 !important;
		font-weight: bold !important;
	}
}
</style>
