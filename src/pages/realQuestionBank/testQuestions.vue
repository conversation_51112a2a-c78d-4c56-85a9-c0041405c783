<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="真题库"
				title=" "
				leftIconSize="25"
				:bgColor="bgColor"
				:autoBack="true"
				safeAreaInsetTop
				placeholder>
				<view
					class="u-nav-slot"
					slot="left">
					<u-icon
						name="arrow-left"
						size="20"
						color="black"></u-icon>
					<image
						:src="imagebaseurl + 'static/icon/time-icon-2.png'"
						mode=""></image>
					<u-count-down
						:time="30 * 60 * 60 * 1000"
						format="HH:mm:ss"></u-count-down>
				</view>
			</u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="main-top">
				<view
					class="search"
					@click="openPicker">
					<view class="search-content">
						<text>{{ realQuestionBank.oldChildenRightItem.customizeName }}</text>
					</view>
					<image
						:src="imagebaseurl + 'static/icon/arrow-down.png'"
						mode=""></image>
				</view>
				<image
					@click="show = true"
					class="dati-image"
					:src="imagebaseurl + 'static/icon/dati_icon.png'"
					mode=""></image>
			</view>
			<swiper
				class="swiper"
				:current="params.userSubjectIndex"
				@change="change"
				circular>
				<swiper-item
					v-for="item in userTopice"
					:key="item.id">
					<scroll-view
						scroll-y="true"
						class="scroll-Y">
						<view class="swiper-box">
							<view class="card">
								<view class="card-top">
									<view
										class="card-top-box"
										style="gap: 10rpx; margin-left: 30rpx">
										<image
											class="icon-1"
											:src="imagebaseurl + 'static/icon/dati_icon_2.png'"
											mode=""></image>
										<text class="card-top-title">{{ realQuestionBank.oldChildenRightItem.customizeName }}</text>
									</view>
								</view>
								<view class="title">
									<text class="title-sing">{{ subjectType[item.subjectType - 1] }}</text>
									<u-parse :content="item.subjectTitle"></u-parse>
									<!-- <text class="title-content">{{ item.subjectTitle }}</text> -->
								</view>
								<view class="card-content">
									<scroll-view
										scroll-y="true"
										style="height: 520rpx">
										<u--textarea
											v-if="
												userTopice[params.userSubjectIndex].userSubjectAnswerExt == null ||
												userTopice[params.userSubjectIndex].userSubjectAnswerExt == ''
											"
											placeholder="请输入答案"
											border="none"
											:maxlength="-1"
											v-model="userSubjectAnswer.userSubjectAnswerExt"
											height="470rpx"></u--textarea>
										<u--textarea
											v-else
											placeholder="请输入答案"
											border="none"
											:maxlength="-1"
											disabled
											v-model="userTopice[params.userSubjectIndex].userSubjectAnswerExt"
											height="470rpx"></u--textarea>
									</scroll-view>
								</view>
								<view
									class="card-upload-title"
									v-if="
										userTopice[params.userSubjectIndex].userSubjectAnswer == '' ||
										userTopice[params.userSubjectIndex].userSubjectAnswer == null
									">
									<text>上传图片</text>
									<text>限3张</text>
								</view>
								<view
									class="card-upload"
									v-if="
										userTopice[params.userSubjectIndex].userSubjectAnswer == null ||
										userTopice[params.userSubjectIndex].userSubjectAnswer == ''
									">
									<view
										class="upload-box"
										v-for="(item, index) in fileList">
										<view
											class="upload"
											@click="showMediaAlert(index)"
											v-if="item.fileUrl == ''">
											<image
												style="width: 60.75rpx; height: 54.44rpx"
												:src="imagebaseurl + 'static/icon/upload.png'"
												mode=""></image>
										</view>

										<view
											v-else
											class="prewimage-box">
											<image
												class="prewimage"
												:src="item.fileUrl"
												@click="prewiewImage(index)"></image>
											<image
												class="delte-icon"
												:src="imagebaseurl + 'static/icon/btn_delet.png'"
												@click="deletePic(index)"></image>
										</view>
									</view>
								</view>
								<view
									class="card-upload"
									v-if="userTopice[params.userSubjectIndex].userSubjectAnswer.length > 0">
									<view
										class="upload"
										v-for="(item, index) in userTopice[params.userSubjectIndex].userSubjectAnswer">
										<image
											:src="item"
											@click="previewImages(userTopice[params.userSubjectIndex], index)"
											class="upload-image"
											mode=""></image>
									</view>
								</view>
							</view>
							<view class="connect">
								<image
									:src="imagebaseurl + 'static/icon/connect.png'"
									mode=""></image>
								<image
									:src="imagebaseurl + 'static/icon/connect.png'"
									mode=""></image>
							</view>
							<view class="card-answer">
								<view class="top-icon">
									<image
										class="answer_correct-icon"
										:src="imagebaseurl + 'static/icon/answer_correct.png'"
										mode=""></image>
									<image
										class="answer-icon"
										:src="imagebaseurl + 'static/icon/answer.png'"
										mode=""></image>
								</view>
								<view class="answer-content">
									<!-- 	<scroll-view
										scroll-y="true"
										style="height: 828rpx">
										
									</scroll-view> -->
									<u-parse :content="item.subjectAnswer"></u-parse>
								</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
			<view class="foot">
				<view class="bottom-page">
					<view
						:class="switchPageBoolean == false ? 'bottom-page-box active' : 'bottom-page-box'"
						@click="switchPage(false)">
						<u-icon
							name="arrow-left"
							:color="switchPageBoolean == false ? 'white' : 'blacke'"></u-icon>
					</view>
					<view class="bottom-page-box-c">
						<view class="pageNume">
							<text>{{ params.userSubjectIndex + 1 }}</text>
							<text>/</text>
							<text>{{ params.userSubjectEndIndex + 1 }}</text>
						</view>
					</view>

					<view
						:class="switchPageBoolean == true ? 'bottom-page-box active' : 'bottom-page-box'"
						@click="switchPage(true)">
						<u-icon
							name="arrow-right"
							:color="switchPageBoolean == true ? 'white' : 'blacke'"></u-icon>
					</view>
				</view>
				<view class="bottom">
					<view class="bottom-box-1">
						<view class="pageNume">
							<text>{{ params.userSubjectIndex + 1 }}</text>
							<text>/</text>
							<text>{{ params.userSubjectEndIndex + 1 }}</text>
						</view>
						<text>正在答题</text>
					</view>
					<view
						class="bottom-box-2"
						@click="confirmCollection">
						<image
							v-if="userTopice[params.userSubjectIndex].isCollect == 1"
							:src="imagebaseurl + 'static/icon/xx_icon_2.png'"
							mode=""></image>
						<image
							v-if="userTopice[params.userSubjectIndex].isCollect == 0"
							:src="imagebaseurl + 'static/icon/xx_icon.png'"
							mode=""></image>
						<text>收藏</text>
					</view>
					<view
						class="bottom-box-3"
						@click="submitAnswer">
						<text>确定</text>
					</view>
				</view>
			</view>
		</view>
		<view
			class=""
			v-if="showYear">
			<u-picker
				:show="showYear"
				:columns="questionList"
				:defaultIndex="[defaultIndex]"
				keyName="customizeName"
				@cancel="showYear = false"
				@confirm="confirmYear"></u-picker>
		</view>
		<u-popup
			:show="show"
			@close="close"
			@open="open">
			<view class="answer-sheet">
				<view class="sheet-top">
					<text>答题卡</text>
					<image
						@click="show = false"
						:src="imagebaseurl + 'static/icon/close_icon_1.png'"
						mode=""></image>
				</view>
				<view class="sheet-box">
					<view
						:class="item.isAnswer == 1 ? 'sheet-item active' : ' sheet-item'"
						v-for="(item, index) in userTopice"
						:key="index"
						@click="selectTopic(item.subjectIndex)">
						<text>{{ item.subjectIndex }}</text>
					</view>
				</view>
			</view>
		</u-popup>
		<van-action-sheet
			:show="showSheet"
			:actions="actions"
			cancel-text="取消"
			close-on-click-action
			@select="onSelect"
			@cancel="onCancel" />
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import {
	realSubjectRepositoryGetSubjectRepositoryPage,
	realSubjectRepositoryGetCustomizeList,
	cancleCollection,
	addCollection,
	reportUserSubjectAnswer,
	getUploadConfigUrl,
	getFileUrl,
} from "../../api/api.js";
import { initQiniu, qiniuUploader } from "../../api/qiniu_index.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			fileList: [{ fileKey: "", fileUrl: "" }],
			show: false,
			background: ["color1", "color2", "color3"],
			indicatorDots: true,
			autoplay: true,
			interval: 2000,
			duration: 500,

			params: {
				// pageNo: 1,
				// pageSize: 10,
				appSubjectRepositoryId: 0,
				userSubjectIndex: 0,
			},
			userTopice: [],
			realQuestionBank: uni.getStorageSync("realQuestionBank"),
			switchPageBoolean: true,
			user: uni.getStorageSync("user"),
			questionList: [[]],
			// 题目类型 1单选 2多选 3不定选项 4判断题 5填空题 6 问答题 7材料题
			subjectType: ["单选", "多选", "不定选项", "判断题", "填空题", "简答题", "材料题"],
			showYear: false,
			uptoken: "",
			fileKey: "",
			userSubjectAnswer: {
				answerType: 4,
				userSubjectAnswerExt: "",
			},
			actions: [{ name: "相册选择" }, { name: "相机" }],
			showSheet: false,
			selectIndex: 0,
			defaultIndex: 0,
			nexPageBoolean: true,
		};
	},
	onLoad(option) {
		this.params.appSubjectRepositoryId = Number(option.appSubjectRepositoryId);
		getUploadConfigUrl({ fileName: "", type: "pic" })
			.then(res => {
				if (res.result == "1") {
					this.fileKey = res.data.fileKey;
					this.uptoken = res.data.token;
				} else {
					this.$showToast(res.message || "查询失败请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询失败请重试");
			});
	},
	mounted() {
		this.getElementHeight();
		this.getUserTopic();
		this.getSchoolTopicListChildern();
	},
	methods: {
		openPicker() {
			this.defaultIndex = this.questionList[0].findIndex(item => item.customizeId == this.realQuestionBank.oldChildenRightItem.customizeId);
			this.showYear = true;
		},
		previewImages(item, index) {
			uni.previewImage({
				current: index,
				urls: [...item.userSubjectAnswer],
			});
		},
		submitAnswer() {
			if (this.toastTopic()) {
				return;
			}
			this.userSubjectAnswer.subjectId = this.userTopice[this.params.userSubjectIndex].id;
			this.userSubjectAnswer.subjectRepositoryId = this.realQuestionBank.oldChildenRightItem.repositoryId;
			if (!this.userSubjectAnswer.userSubjectAnswerExt) {
				this.$showToast("请输入答案");
				return;
			}
			//图片的key
			let userSubjectAnswer = [];
			//图片的可访问路径
			let userSubjectAnswerImage = [];
			this.fileList.map(v => {
				if (v.fileKey) {
					userSubjectAnswer.push(v.fileKey);
					userSubjectAnswerImage.push(v.fileUrl);
				}
			});
			this.userSubjectAnswer.userSubjectAnswer = userSubjectAnswer.join(",");
			reportUserSubjectAnswer({
				subjectRepositoryId: this.realQuestionBank.oldChildenRightItem.repositoryId,
				userSubjectAnswer: this.userSubjectAnswer,
			})
				.then(res => {
					console.log("提交答案", res);
					if (res.result == "1") {
						//把题目和图片暂时存入原数据中不调用接口更新，下次进入自动更新
						this.userTopice[this.params.userSubjectIndex].userSubjectAnswerExt = this.userSubjectAnswer.userSubjectAnswerExt;
						if (userSubjectAnswer.length > 0) {
							this.userTopice[this.params.userSubjectIndex].userSubjectAnswer = userSubjectAnswerImage;
						}
						this.userTopice[this.params.userSubjectIndex].isAnswer = 1;
						this.toastTopic();
						//下一题
						if (this.params.userSubjectIndex < this.params.userSubjectEndIndex) {
							this.params.userSubjectIndex++;
						}
						//数据还原
						this.userSubjectAnswer = {
							answerType: 4,
							userSubjectAnswerExt: "",
						};
						this.fileList = [{ fileKey: "", fileUrl: "" }];
					} else {
						this.$showToast(res.message || "提交失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "提交失败请重试");
				});
		},
		toastTopic() {
			let index = this.userTopice.findIndex(item => item.isAnswer == 0);
			console.log("是否完成全部题目", index);
			if (index == -1) {
				this.$showToast("您已完成全部题目");
				return true;
			}
		},
		//收藏
		confirmCollection() {
			if (this.userTopice[this.params.userSubjectIndex].isCollect == 1) {
				const params = {
					subjectId: this.userTopice[this.params.userSubjectIndex].id,
					collectType: 5,
				};
				cancleCollection(params)
					.then(res => {
						if (res.result == "1") {
							this.$showToast("取消收藏成功");
							this.userTopice[this.params.userSubjectIndex].isCollect = 0;
						} else {
							this.$showToast(res.message || "取消收藏失败请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "取消收藏失败请重试");
					});
			}
			if (this.userTopice[this.params.userSubjectIndex].isCollect == 0) {
				//添加收藏
				const params = {
					subjectId: this.userTopice[this.params.userSubjectIndex].id,
					collectType: 5,
					subjectRepositoryId: this.realQuestionBank.oldChildenRightItem.repositoryId,
				};
				addCollection(params)
					.then(res => {
						if (res.result == "1") {
							this.$showToast("收藏成功");
							this.userTopice[this.params.userSubjectIndex].isCollect = 1;
						} else {
							this.$showToast(res.message || "收藏失败请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "收藏失败请重试");
					});
			}
		},
		//答题卡选择题目
		selectTopic(index) {
			this.params.userSubjectIndex = index - 1;
			this.fileList = [{ fileKey: "", fileUrl: "" }];
			this.show = false;
		},
		//分页
		switchPage(boolean) {
			console.log("当前题目页数：", this.params.userSubjectIndex);
			if (boolean) {
				if (this.params.userSubjectIndex < this.params.userSubjectEndIndex) {
					this.params.userSubjectIndex++;
				}
			} else {
				if (this.params.userSubjectIndex > 0) {
					this.params.userSubjectIndex--;
				}
			}
			this.switchPageBoolean = boolean;
		},
		//用户主动滑动分页
		change(e) {
			this.params.userSubjectIndex = e.detail.current;
		},
		//获取用户题目
		getUserTopic() {
			realSubjectRepositoryGetSubjectRepositoryPage(this.params)
				.then(res => {
					if (res.result == "1") {
						this.userTopice = res.data.list;
						this.params.userSubjectEndIndex = res.data.list[0].maxSubjectIndex - 1;
						this.params.userSubjectIndex = this.realQuestionBank.oldChildenRightItem.userSubjectIndex;
						// 当用户全部答题后,页面显示的当前题目数会加1,所以这里会减1保存页面显示一致
						if (this.params.userSubjectIndex == res.data.list[0].maxSubjectIndex) {
							this.params.userSubjectIndex = res.data.list[0].maxSubjectIndex - 1;
						}
						this.userTopice.map(v => {
							if (v.userSubjectAnswer) {
								v.userSubjectAnswer = v.userSubjectAnswer.split(",");
							}
						});
					} else {
						this.$showToast(res.message || "获取题目失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取题目失败请重试");
				});
		},
		//分页调用，这里不会重置当前题目的位置
		pageLecture() {
			if (this.params.userSubjectEndIndex - this.params.userSubjectIndex > 3) {
				if (this.nexPageBoolean) {
					this.params.pageNo++;
					realSubjectRepositoryGetSubjectRepositoryPage(this.params)
						.then(res => {
							if (res.result == "1") {
								if (res.data.list.length == 0) {
									this.nexPageBoolean = false;
								}
								this.userTopice = [...this.userTopice, ...res.data.list];
							} else {
								this.$showToast(res.message || "获取题目失败请重试");
							}
						})
						.catch(err => {
							this.$showToast(err.message || "获取题目失败请重试");
						});
				}
			}
		},
		open() {
			// console.log('open');
		},
		close() {
			this.show = false;
			// console.log('close');
		},
		confirmYear(event) {
			this.realQuestionBank.oldChildenRightItem = event.value[0];
			this.params.appSubjectRepositoryId = this.realQuestionBank.oldChildenRightItem.repositoryId;
			this.params.userSubjectIndex = 0;
			this.fileList = [{ fileKey: "", fileUrl: "" }];
			this.getUserTopic();
			this.getSchoolTopicListChildern();
			this.showYear = false;
		},
		//查询课程下的专业 - 真题题库
		getSchoolTopicListChildern() {
			realSubjectRepositoryGetCustomizeList({
				masterType: this.user.masterType,
				courseId: this.realQuestionBank.selectSubLeftItem.courseId,
				professionId: this.realQuestionBank.selectRighttItem.professionId,
				schoolId: this.realQuestionBank.selectLeftItem.schoolId,
			})
				.then(res => {
					if (res.result) {
						this.questionList[0] = res.data.list;
					} else {
						this.$showToast(res.message || "获取真题题库失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取真题题库失败请重试");
				});
		},

		showMediaAlert(index) {
			this.showSheet = true;
			this.selectIndex = index;
		},
		onCancel() {
			this.showSheet = false;
		},
		onSelect(item) {
			let self = this;
			if (item.detail.name == "相册选择") {
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["album"], //从相册选择
					success: function (res) {
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			} else if (item.detail.name == "相机") {
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["camera"], //从相册选择
					success: function (res) {
						console.log(res);
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			}
			this.showSheet = false;
		},
		uploadFileResp(tempFilePaths) {
			var filePath = tempFilePaths[0];
			getUploadConfigUrl({ fileName: "", type: "pic" }).then(res => {
				console.log("七牛云参数：", res);
				initQiniu({ uptoken: res.data.token });
				// 向七牛云上传
				qiniuUploader.upload(
					filePath,
					res => {
						const result = JSON.parse(JSON.stringify(res));
						getFileUrl({ fileKey: result.key, type: "pic" })
							.then(resFile => {
								if (resFile.result == "1") {
									this.fileList[this.selectIndex].fileKey = result.key;
									this.fileList[this.selectIndex].fileUrl = resFile.data;
									if (this.fileList.length <= 3) {
										this.fileList.push({
											fileKey: "",
											fileUrl: "",
										});
									}
								} else {
									this.$showToast(resFile.message || "上传失败");
								}
							})
							.catch(err => {
								this.$showToast(err.message || "上传失败");
							});
					},
					error => {
						console.error("error: " + JSON.stringify(error));
						this.$showToast(JSON.stringify(error) || "上传失败");
					},
					{
						region: "NCN", // 华北区
						uptokenURL: "",
						domain: "",
						shouldUseQiniuFileName: false,
						key: res.data.fileKey,
						uptokenURL: "",
					}
				);
			});
		},
		// 删除图片
		deletePic(index) {
			this.fileList.splice(index, 1);
		},
		prewiewImage(index) {
			let urls = [];
			this.fileList.map(item => {
				if (item.fileUrl) {
					urls.push(item.fileUrl);
				}
			});
			uni.previewImage({
				urls: urls,
				current: index,
			});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
.swiper {
	margin-top: 26rpx;
	width: calc(100%);
	height: 1100rpx;
	padding-bottom: 100rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.swiper-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 100rpx;
	.card {
		width: calc(100% - 80rpx);
		// height: 1100rpx;
		padding-bottom: 50rpx;
		background: #ffffff;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.card-top {
			width: 100%;
			height: 100rpx;
			background: linear-gradient(44deg, #c6d8f1 0%, #dae6f6 100%);
			border-radius: 30rpx 30rpx 0rpx 0rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.card-top-box {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				.icon-1 {
					width: 32.91rpx;
					height: 35.15rpx;
				}
				.card-top-title {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 36rpx;
					color: #1f4085;
				}
			}
		}

		.title {
			margin-top: 24rpx;
			width: calc(100% - 50rpx);
			display: flex;
			flex-direction: column;
			gap: 12rpx;
			.title-sing {
				width: 96rpx;
				height: 40rpx;
				background: #2168fe;
				border-radius: 6rpx 6rpx 6rpx 6rpx;
				border: 2rpx solid #2168fe;
				display: flex;
				justify-content: center;
				align-items: center;

				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #ffffff;
			}
			.title-content {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
		}
		.card-content {
			margin-top: 20rpx;
			width: calc(100% - 30rpx);
			height: 540rpx;
			background: #f7f8fa;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
		}
		.card-upload-title {
			margin-top: 20rpx;
			width: calc(100% - 30rpx);
			display: flex;
			justify-content: flex-start;
			align-items: flex-end;
			gap: 10rpx;
			text:nth-child(1) {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
			text:nth-child(2) {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #8590a7;
			}
		}
		.card-upload {
			margin-top: 20rpx;
			width: calc(100% - 30rpx);
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 20rpx;
			.upload {
				width: 202rpx;
				height: 202rpx;
				background: #f7f8fa;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 20rpx;
				.upload-image {
					width: 202rpx;
					height: 202rpx;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
				}
			}
			.upload-box {
				.upload {
					width: 202rpx;
					height: 202rpx;
					background: #f7f8fa;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				.prewimage-box {
					width: 202rpx;
					height: 202rpx;
					postion: relative;
					display: flex;
					justify-content: flex-end;
					.prewimage {
						position: absolute;
						width: 202rpx;
						height: 202rpx;
						border-radius: 20rpx 20rpx 20rpx 20rpx;
					}
					.delte-icon {
						position: relative;
						width: 36rpx;
						height: 36rpx;
						left: 10rpx;
						top: -7rpx;
					}
				}
			}
		}
	}
	.connect {
		width: calc(100% - 244rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
		top: -20rpx;
		z-index: 99;
		image {
			width: 16rpx;
			height: 68rpx;
		}
	}
	.card-answer {
		width: calc(100% - 80rpx);
		// height: 1026rpx;
		background: #ffffff;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		top: -44rpx;
		.top-icon {
			margin-top: 50rpx;
			width: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			position: relative;
			left: -10rpx;
			.answer_correct-icon {
				position: absolute;
				top: 12rpx;
				left: 19rpx;
				width: 40rpx;
				height: 40rpx;
			}
			.answer-icon {
				width: 230rpx;
				height: 81.92rpx;
			}
		}
		.answer-content {
			margin-top: 20rpx;
			width: calc(100% - 60rpx);
			padding-bottom: 50rpx;
			// height: 828rpx;
			// border: 1rpx solid;
		}
	}
}
.scroll-Y {
	height: 1100rpx;
}
::v-deep .u-textarea {
	background: none !important;
}
::v-deep .u-upload.data-v-49deb6f2 {
	align-items: center;
}
::v-deep .u-count-down__text {
	font-family: DINPro, DINPro;
	font-weight: bold;
	font-size: 36rpx;
	color: #2168fe !important;
}
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-upload__wrap__preview__image {
	width: 202rpx !important;
	height: 202rpx !important;
	border-radius: 20rpx 20rpx 20rpx 20rpx !important;
}
::v-deep .u-upload__wrap__preview.data-v-49deb6f2 {
	border-radius: 20rpx 20rpx 20rpx 20rpx !important;
	margin: 0 !important;
	// position: relative;
	// overflow: hidden;
	// display: flex;
	// flex-direction: row;
}
::v-deep .u-navbar__content__left.data-v-75dad532,
.u-navbar__content__right {
	padding: 0 !important;
}
.answer-sheet {
	width: 100%;
	height: 600rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.sheet-top {
		width: 100%;
		height: 100rpx;
		border-bottom: 2rpx solid #edeef2;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 283rpx;

		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #1f2638;
		}
		image {
			margin-right: 18rpx;
			width: 32.83rpx;
			height: 32.83rpx;
		}
	}
	.sheet-box {
		margin-top: 40rpx;
		width: calc(100% - 90rpx);
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-items: center;
		gap: 60rpx;
		.sheet-item {
			width: 80rpx;
			height: 80rpx;
			background: #f7f7f9;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			border: 1rpx solid #dde1e9;
			display: flex;
			justify-content: center;
			align-items: center;
			text {
				font-family: DINPro, DINPro;
				font-weight: 500;
				font-size: 36rpx;
				color: #1f2638;
			}
		}
		.active {
			background: #2168fe;
			text {
				color: #ffffff;
			}
		}
	}
}
.u-nav-slot {
	padding-left: 28rpx;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 20rpx;
	image {
		width: 30rpx;
		height: 30rpx;
	}
	.top-title {
		font-weight: bold !important;
		font-size: 36rpx !important;
		color: #1f2638 !important;
	}
	.top-select {
		width: 128rpx;
		height: 48rpx;
		background: #94b2d6;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 10rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 24rpx;
		color: #ffffff;
	}
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.foot {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: fixed;
	bottom: 0;
	padding-bottom: 20rpx;
	background-color: #f6f7fb;
}
.bottom-page {
	width: calc(100% - 80rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	.bottom-page-box {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100rpx;
		height: 60rpx;
		background: #f7f7f9;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 1rpx solid #dde1e9;
	}
	.active {
		background: #2168fe;
	}
	.bottom-page-box-c {
		width: 200rpx;
		height: 60rpx;
		background: #f7f7f9;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		.pageNume {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 5rpx;
			text:nth-child(1) {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 24rpx;
				color: #2168fe;
			}
			text {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 24rpx;
				color: #a8aebe;
			}
		}
	}
}
.bottom {
	margin-top: 20rpx;
	width: calc(100% - 90rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	.bottom-box-1 {
		display: flex;
		flex-direction: column;
		align-items: center;
		.pageNume {
			display: flex;
			justify-content: center;
			align-items: baseline;
			gap: 5rpx;
			text:nth-child(1) {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 40rpx;
				color: #1f2638;
			}
			text {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 24rpx;
				color: #8590a7;
			}
		}
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 22rpx;
			color: #8590a7;
		}
	}
	.bottom-box-2 {
		width: 180rpx;
		height: 80rpx;
		background: #f8f9fb;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(129, 147, 174, 0.14);
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 8rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #1f2638;
		}
		image {
			width: 31.09rpx;
			height: 30.39rpx;
		}
	}
	.bottom-box-3 {
		width: 300rpx;
		height: 80rpx;
		background: #2168fe;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #ffffff;
		}
	}
}

.line {
	width: 2rpx;
	height: 28rpx;
	background: linear-gradient(180deg, rgba(123, 126, 154, 0) 0%, rgba(123, 126, 154, 0.5) 52%, rgba(123, 126, 154, 0) 100%);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
}
.main-top {
	width: calc(100% - 80rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	.dati-image {
		width: 50.55rpx;
		height: 51.18rpx;
	}
}
.search {
	width: calc(100% - 295rpx);
	height: 76rpx;
	background: rgba(31, 64, 133, 0.1);
	border-radius: 38rpx 38rpx 38rpx 38rpx;
	border: 2rpx solid #1f4085;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.search-content {
		margin-left: 20rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 12rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #212838;
		}
	}
	image {
		margin-right: 20rpx;
		width: 24rpx;
		height: 24rpx;
	}
}
</style>
