<template>
	<view class="container">
		<view class="top">
			<u-navbar
				:leftText="title"
				leftIconColor="#212838"
				autoBack
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="search">
				<view class="search-content">
					<image
						:src="imagebaseurl + 'static/icon/search-icon.png'"
						mode=""></image>
					<u--input
						placeholder="搜索科目"
						border="none"
						v-model="params.keyWord"
						@change="search"></u--input>
				</view>
				<view
					class="search-right"
					@click="clear">
					<image
						:src="imagebaseurl + 'static/icon/clean.png'"
						mode=""></image>
					<text>清除所有</text>
				</view>
			</view>
			<view
				class="card"
				v-for="(item, index) in majorList"
				:key="index">
				<view
					class="card-top"
					@click="slectOpen(item, index)">
					<view class="card-left">
						<image
							:src="imagebaseurl + 'static/icon/courses_icon_4.png'"
							mode=""></image>
						<text>{{ item.professionName }}</text>
					</view>
					<image
						class="card-right"
						:src="imagebaseurl + 'static/icon/arrow_down_icon.png'"
						mode="" />
				</view>
				<view
					:class="selectMajor.professionId == item.professionId ? 'card-boyd ' : 'card-boyd'"
					v-if="selectMajor.professionId == item.professionId">
					<text
						:class="selectcCourse.repositoryId == item2.repositoryId ? 'u-collapse-content active' : 'u-collapse-content '"
						v-for="(item2, index2) in courseList"
						:key="index2"
						@click="change(item2)">
						{{ item2.chapterName }}
					</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getHoldProfessionListe, getHoldChapterList, getCollectProfessionList, getCollectChapterList, clearCollection } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			iconUrl: require("../../static/icon/courses_icon_4.png"),
			user: uni.getStorageSync("user"),
			params: {
				pageNo: 1,
				pageSize: 10,
				// 0 未掌握 1 未完全掌握 2 已掌握 3我的收藏
				subjectHold: 0,
			},
			majorList: [],
			courseList: [],
			selectcCourse: {},
			title: "我的讲义",
			params_: {
				pageNo: 1,
				pageSize: 10,
			},
			debounceTimeout: null,
			selectMajor: {},
			selectIndex: -1,
			courseHeight: 0,
		};
	},
	onLoad(option) {
		this.params.subjectHold = option.subjectHold;
		this.params.masterType = this.user.masterType;
		this.params.globalSchoolId = this.user.globalSchoolId;
		this.params.globalProfessionId = this.user.globalProfessionId;
		if (this.params.subjectHold == 0) {
			this.title = "未掌握";
		} else if (this.params.subjectHold == 1) {
			this.title = "未完全掌握";
		} else if (this.params.subjectHold == 2) {
			this.title = "已掌握";
		} else if (this.params.subjectHold == -1) {
			this.title = "我的收藏";
		}
		if (this.params.subjectHold != -1) {
			this.getUserLectureNotes();
		}
		if (this.params.subjectHold == -1) {
			this.getCollectProfession();
		}
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		slectOpen(item, index) {
			this.courseList = [];
			if (this.selectMajor.professionId == item.professionId) {
				this.selectMajor = {};
			} else {
				this.selectMajor = item;
				this.selectIndex = index;
				if (this.params.subjectHold != -1) {
					this.getCourseList();
				}
				if (this.params.subjectHold == -1) {
					this.getCollectChapter();
				}
			}
		},
		//搜索
		search() {
			// 清除上一次的延时器
			if (this.debounceTimeout) {
				clearTimeout(this.debounceTimeout);
			}
			// 设置新的延时器
			this.debounceTimeout = setTimeout(() => {
				this.list = [];
				this.params.pageNo = 1;
				this.params.pageSize = 10;
				if (this.params.subjectHold != -1) {
					this.getUserLectureNotes();
				}
				if (this.params.subjectHold == -1) {
					this.getCollectProfession();
				}
			}, 300); // 设置延时300毫秒
		},
		clear() {
			uni.showModal({
				title: "提示",
				content: "确定要清除所有吗？",
				success: res => {
					if (res.confirm) {
						clearCollection({
							collectType: 2,
							subjectHold: this.params.subjectHold,
						})
							.then(res => {
								if (res.result == "1") {
									this.$showToast("清除成功");
									if (this.params.subjectHold != -1) {
										this.getUserLectureNotes();
									}
									if (this.params.subjectHold == -1) {
										this.getCollectProfession();
									}
								} else {
									this.$showToast(res.message || "操作失败请重试");
								}
							})
							.catch(err => {
								this.$showToast(err.message || "操作失败请重试");
							});
					}
				},
			});
		},
		open(e) {},
		change(item) {
			console.log(item);
			this.selectcCourse = item;
			let userSubject = uni.getStorageSync("userSubject");
			userSubject.oldChildenRightItem = item;
			userSubject.name = userSubject.selectRighttItem.professionName + "-" + userSubject.oldChildenRightItem.chapterName;
			uni.setStorageSync("userSubject", userSubject);
			uni.navigateTo({
				url: "/pages/lectureNotes/lectureNotesDeatil?lectureNotesRepositoryId=" + item.repositoryId,
			});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		// 收藏的讲义
		getCollectProfession() {
			this.params.masterType = this.user.masterType;
			getCollectProfessionList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.majorList = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		getCollectChapter() {
			getCollectChapterList({
				masterType: this.user.masterType,
				professionId: this.selectMajor.professionId,
				globalSchoolId: this.user.globalSchoolId,
				globalProfessionId: this.user.globalProfessionId,
			})
				.then(res => {
					if (res.result == "1") {
						this.courseList = res.data.list;
						this.courseHeight = this.courseList.length * 82;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		// 讲义的掌握情况
		getUserLectureNotes() {
			getHoldProfessionListe(this.params)
				.then(res => {
					if (res.result == "1") {
						this.majorList = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
		getCourseList() {
			getHoldChapterList({
				masterType: this.user.masterType,
				professionId: this.selectMajor.professionId,
				subjectHold: this.params.subjectHold,
				globalSchoolId: this.user.globalSchoolId,
				globalProfessionId: this.user.globalProfessionId,
			})
				.then(res => {
					// 选择我们想要的元素
					if (res.result == "1") {
						this.courseList = res.data.list;
					} else {
						this.$showToast(res.message || "查询失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败请重试");
				});
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
::v-deep .u-cell__title-text {
	margin-left: 10rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 28rpx;
	color: #1f2638;
}

.card {
	margin-top: 24rpx;
	width: calc(100% - 60rpx);
	min-height: 82rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	overflow: hidden;
	.card-top {
		width: calc(100% - 80rpx);
		padding-top: 24rpx;
		padding-bottom: 26rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.card-left {
			display: flex;
			align-items: center;
			gap: 18rpx;
			image {
				width: 36.82rpx;
				height: 36.24rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #1f2638;
			}
		}
		.card-right {
			width: 16.17rpx;
			height: 9.95rpx;
		}
	}
	.card-boyd {
		width: calc(100%);
		padding-top: 26rpx;
		border-top: 1rpx solid #ebecf0;
		display: flex;
		flex-direction: column;
		.u-collapse-content {
			width: 500rpx;
			margin-left: 82rpx;
			margin-top: 12rpx;
			margin-bottom: 35rpx;
		}
		.active {
			color: #2168fe;
		}
	}
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
	padding-bottom: 100rpx;
}

.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.collpage {
	margin-top: 24rpx;
	width: calc(100% - 60rpx);
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
}

.search {
	width: calc(100% - 50rpx);

	display: flex;
	justify-content: space-between;
	align-items: center;
	.search-content {
		width: calc(100% - 200rpx);
		height: 76rpx;
		background: #ffffff;
		border-radius: 38rpx 38rpx 38rpx 38rpx;
		border: 2rpx solid #c8dcf1;
		display: flex;
		align-items: center;
		gap: 20rpx;
		image {
			margin-left: 32rpx;
			width: 30.63rpx;
			height: 30.85rpx;
		}
	}
	.search-right {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 10rpx;
		image {
			width: 30.78rpx;
			height: 29.68rpx;
		}
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 26rpx;
			color: #1f4085;
		}
	}
}
.scale-in-ver-top {
	-webkit-animation: scale-in-ver-top 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
	animation: scale-in-ver-top 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2025-1-4 15:4:39
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-in-ver-top
 * ----------------------------------------
 */
@-webkit-keyframes scale-in-ver-top {
	0% {
		height: 82rpx;
		opacity: 1;
	}
	100% {
		height: 100%;
		opacity: 1;
	}
}
@keyframes scale-in-ver-top {
	0% {
		height: 82rpx;
		opacity: 1;
	}
	100% {
		height: 100%;
		opacity: 1;
	}
}
</style>
