<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="我的讲义"
				title=" "
				leftIconSize="25"
				:bgColor="bgColor"
				:autoBack="true"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="card-logo">
				<image
					:src="imagebaseurl + 'static/icon/userHandout_icon.png'"
					mode=""></image>
				<text>LIGEKAOYA</text>
			</view>
			<view
				class="card"
				v-for="item in list"
				:key="item.id"
				@click="goMyLectureNotes(item)">
				<view class="card-left">
					<image
						:src="imagebaseurl + item.image"
						mode=""></image>
					<text>{{ item.name }}</text>
				</view>
				<!-- <CircleProgress
					:percent="item.percentage"
					:width="120"
					inactiveColor="#DEE8F6"
					:activeColor="item.color"
					borderWidth="12">
					<view class="circle-progress-content">
						<text class="text-1">{{ item.percentage }}</text>
						<text>%</text>
					</view>
				</CircleProgress> -->
				<van-circle
					v-model="item.percentage"
					:rate="100"
					:size="55"
					:speed="100"
					color="#2168FE"
					layer-color="#DEE8F6">
					<template #default>
						<view class="circle-progress-content">
							<text class="text-1">{{ item.percentage }}</text>
							<text>%</text>
						</view>
					</template>
				</van-circle>
				<view class="card-right">
					<text>{{ item.count }}道</text>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CircleProgress from "../../component/comment/CircleProgress.vue";
import { imagebaseurl } from "../../api/index.js";
import { getUserLectureNotesStatistical } from "../../api/api.js";
export default {
	components: { CircleProgress },
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			percent: 60,
			list: [
				{ id: 0, name: "未掌握", image: "static/icon/userHandout_icon_1.png", color: "#FF4D4D", count: 0, percentage: 0 },
				{ id: 1, name: "未完全掌握", image: "static/icon/userHandout_icon_2.png", color: "#FF6D27", count: 0, percentage: 0 },
				{ id: 2, name: "已掌握", image: "static/icon/userHandout_icon_3.png", color: "#2168FE", count: 0, percentage: 0 },
				{ id: -1, name: "我的收藏", image: "static/icon/menu_item_8.png", color: "#FFB41F", count: 0, percentage: 0 },
			],
			user: uni.getStorageSync("user"),
		};
	},
	mounted() {
		this.getElementHeight();
		getUserLectureNotesStatistical({
			masterType: this.user.masterType,
			globalSchoolId: this.user.globalSchoolId,
			globalProfessionId: this.user.globalProfessionId,
		})
			.then(res => {
				if (res.result == "1") {
					res.data.map((item, index) => {
						let listIndex = this.list.findIndex(item1 => {
							return item1.id == item.subjectHold;
						});
						console.log(listIndex);
						this.list[listIndex].count = item.subjectCount;
						this.list[listIndex].percentage = item.percentage || 0;
					});
				} else {
					this.$showToast(res.message || "查询失败请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询失败请重试");
			});
	},
	methods: {
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		goMyLectureNotes(item) {
			uni.navigateTo({
				url: "/pages/userPage/myLectureNotes?subjectHold=" + item.id,
			});
		},
	},
};
</script>

<style lang="scss">
::v-deep .van-circle {
	top: 6rpx;
}
::v-deep .u-count-down__text {
	font-family: DINPro, DINPro;
	font-weight: bold;
	font-size: 36rpx;
	color: #2168fe !important;
}
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.container {
	width: 100%;
	height: 100vh;
	padding-bottom: 100rpx;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.card-logo {
	width: calc(100% - 60rpx);
	height: 300rpx;
	background: #2168fe;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	border: 2rpx solid #ffffff;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	image {
		width: 156rpx;
		height: 156rpx;
	}
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 36rpx;
		color: #ffffff;
		line-height: 70rpx;
		letter-spacing: 1.3rpx;
	}
}
.card {
	margin-top: 24rpx;
	width: calc(100% - 90rpx);
	padding: 20rpx;
	height: 80rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 30rpx;
	border: 2rpx solid #ffffff;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 80rpx;
	.card-left {
		width: calc(100% - 408rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		image {
			width: 80rpx;
			height: 80rpx;
		}
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #1f2638;
		}
	}
	.circle-progress-content {
		display: flex;
		justify-content: center;
		align-items: baseline;
		text {
			font-family: DINPro, DINPro;
			font-weight: bold;
			color: #1f4085;
		}
		text:nth-child(1) {
			font-size: 32rpx;
		}
		text:nth-child(2) {
			font-size: 20rpx;
		}
	}
	.card-right {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 36rpx;
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #1f4085;
		}
		image {
			width: 10.65rpx;
			height: 18.63rpx;
		}
	}
}
</style>
