<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="帮助反馈"
				title=" "
				leftIconSize="25"
				:bgColor="bgColor"
				:autoBack="true"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="main-top">
				<view class="title">
					<text class="title-sing">*</text>
					<text class="title-content">选择反馈问题类型</text>
				</view>
				<view class="main-box">
					<view
						:class="selectPostion == item.id ? 'box active' : 'box'"
						v-for="(item, index) in postionList"
						:key="item.id"
						@click="selectPostion = item.id">
						<text>{{ item.name }}</text>
					</view>
				</view>
			</view>

			<view class="card">
				<view class="title">
					<text class="title-sing">*</text>
					<text class="title-content">请补充详细问题或意见</text>
				</view>
				<view class="card-content">
					<scroll-view
						scroll-y="true"
						style="height: 520rpx">
						<u--textarea
							placeholder="请输入具体问题"
							border="none"
							:maxlength="-1"
							height="470rpx"
							v-model="issueDetail"></u--textarea>
					</scroll-view>
				</view>
				<view class="card-upload-title">
					<view class="card-upload-title-box">
						<text>*</text>
						<text>上传图片</text>
					</view>

					<text>限3张</text>
				</view>
				<view class="card-upload">
					<view
						class="upload-box"
						v-for="(item, index) in fileList">
						<view
							class="upload"
							@click="showMediaAlert(index)"
							v-if="item.fileUrl == ''">
							<image
								style="width: 60.75rpx; height: 54.44rpx"
								:src="imagebaseurl + 'static/icon/upload.png'"
								mode=""></image>
						</view>

						<view
							v-else
							class="prewimage-box">
							<image
								class="prewimage"
								:src="item.fileUrl"
								@click="prewiewImage(index)"></image>
							<image
								class="delte-icon"
								:src="imagebaseurl + 'static/icon/btn_delet.png'"
								@click="deletePic(index)"></image>
						</view>
					</view>
				</view>
			</view>

			<view class="foot">
				<view class="bottom">
					<view
						class="bottom-box-3"
						@click="submit">
						<text>立即提交</text>
					</view>
				</view>
			</view>
		</view>
		<van-action-sheet
			:show="showSheet"
			:actions="actions"
			cancel-text="取消"
			close-on-click-action
			@select="onSelect"
			@cancel="onCancel" />
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getUploadConfigUrl, getFileUrl, submitFeedback } from "../../api/api.js";
import { initQiniu, qiniuUploader } from "../../api/qiniu_index.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			cardType: 0,
			fileList: [{ fileKey: "", fileUrl: "" }],
			show: false,
			background: ["color1", "color2", "color3"],
			indicatorDots: true,
			autoplay: true,
			interval: 2000,
			duration: 500,
			postionList: [
				{ id: 1, name: "功能异常" },
				{ id: 2, name: "体验问题" },
				{ id: 3, name: "改进建议" },
			],
			selectPostion: 1,
			actions: [{ name: "相册选择" }, { name: "相机" }],
			showSheet: false,
			selectIndex: 0,
			issueDetail: "",
			user: uni.getStorageSync("user"),
		};
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		submit() {
			if (this.issueDetail == "") {
				this.$showToast("请输入反馈内容");
				return;
			}
			if (this.fileList[0].fileKey == "") {
				this.$showToast("请上传图片,至少一张");
				return;
			}
			let urls = [];
			this.fileList.forEach(item => {
				urls.push(item.fileKey);
			});

			const issue = this.postionList[this.selectPostion].name;
			submitFeedback({
				issue: issue,
				issueDetail: this.issueDetail,
				masterType: this.user.masterType,
				picList: urls,
			})
				.then(res => {
					console.log("res: ", res);
					if (res.result == "1") {
						this.$showToast("提交成功");
						this.issueDetail = "";
						this.fileList = [{ fileKey: "", fileUrl: "" }];
					} else {
						this.$showToast(res.message || "提交失败请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "提交失败请重试");
				});
		},
		open() {
			// console.log('open');
		},
		close() {
			this.show = false;
			// console.log('close');
		},
		showMediaAlert(index) {
			this.showSheet = true;
			this.selectIndex = index;
		},
		onCancel() {
			this.showSheet = false;
		},
		onSelect(item) {
			let self = this;
			if (item.detail.name == "相册选择") {
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["album"], //从相册选择
					success: function (res) {
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			} else if (item.detail.name == "相机") {
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["camera"], //从相册选择
					success: function (res) {
						console.log(res);
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			}
			this.showSheet = false;
		},
		uploadFileResp(tempFilePaths) {
			var filePath = tempFilePaths[0];
			getUploadConfigUrl({ fileName: "", type: "pic" }).then(res => {
				console.log("七牛云参数：", res);
				initQiniu({ uptoken: res.data.token });
				// 向七牛云上传
				qiniuUploader.upload(
					filePath,
					res => {
						const result = JSON.parse(JSON.stringify(res));
						getFileUrl({ fileKey: result.key, type: "pic" })
							.then(resFile => {
								if (resFile.result == "1") {
									this.fileList[this.selectIndex].fileKey = result.key;
									this.fileList[this.selectIndex].fileUrl = resFile.data;
									if (this.fileList.length <= 3) {
										this.fileList.push({
											fileKey: "",
											fileUrl: "",
										});
									}
								} else {
									this.$showToast(resFile.message || "上传失败");
								}
							})
							.catch(err => {
								this.$showToast(err.message || "上传失败");
							});
					},
					error => {
						console.error("error: " + JSON.stringify(error));
					},
					{
						region: "NCN", // 华北区
						uptokenURL: "",
						domain: "",
						shouldUseQiniuFileName: false,
						key: res.data.fileKey,
						uptokenURL: "",
					}
				);
			});
		},
		// 删除图片
		deletePic(index) {
			this.fileList.splice(index, 1);
		},
		prewiewImage(index) {
			let urls = [];
			this.fileList.map(item => {
				if (item.fileUrl) {
					urls.push(item.fileUrl);
				}
			});
			uni.previewImage({
				urls: urls,
				current: index,
			});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
.card {
	margin-top: 20rpx;
	padding-bottom: 46rpx;
	width: calc(100% - 80rpx);
	background: #ffffff;
	border-radius: 40rpx 40rpx 40rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.card-top {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(44deg, #c6d8f1 0%, #dae6f6 100%);
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.card-top-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.icon-1 {
				width: 32.91rpx;
				height: 35.15rpx;
			}
			.card-top-title {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #1f4085;
			}
		}
	}

	.card-content {
		margin-top: 20rpx;
		width: calc(100% - 44rpx);
		height: 540rpx;
		background: #f7f8fa;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
	}
	.card-upload-title {
		margin-top: 20rpx;
		width: calc(100% - 44rpx);
		display: flex;
		justify-content: flex-start;
		align-items: flex-end;
		gap: 10rpx;
		.card-upload-title-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 5rpx;
			text:nth-child(1) {
				font-size: 20rpx;
				color: red;
			}
			text:nth-child(2) {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
		}

		text:nth-child(2) {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #8590a7;
		}
	}
	.card-upload {
		margin-top: 20rpx;
		width: calc(100% - 44rpx);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 20rpx;
		.upload-box {
			.upload {
				width: 202rpx;
				height: 202rpx;
				background: #f7f8fa;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.prewimage-box {
				width: 202rpx;
				height: 202rpx;
				postion: relative;
				display: flex;
				justify-content: flex-end;
				.prewimage {
					position: absolute;
					width: 202rpx;
					height: 202rpx;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
				}
				.delte-icon {
					position: relative;
					width: 36rpx;
					height: 36rpx;
					left: 10rpx;
					top: -7rpx;
				}
			}
		}
	}
}
.title {
	margin-top: 24rpx;
	width: calc(100% - 50rpx);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 12rpx;
	.title-sing {
		font-size: 24rpx;
		color: red;
	}
	.title-content {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 32rpx;
		color: #1f2638;
	}
}

::v-deep .u-textarea {
	background: none !important;
}
::v-deep .u-upload.data-v-49deb6f2 {
	align-items: center;
}
::v-deep .u-count-down__text {
	font-family: DINPro, DINPro;
	font-weight: bold;
	font-size: 36rpx;
	color: #2168fe !important;
}
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.u-nav-slot {
	padding-left: 28rpx;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 20rpx;
	image {
		width: 28.05rpx;
		height: 27.94rpx;
	}
	.top-title {
		font-weight: bold !important;
		font-size: 36rpx !important;
		color: #1f2638 !important;
	}
	.top-select {
		width: 128rpx;
		height: 48rpx;
		background: #94b2d6;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 10rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 24rpx;
		color: #ffffff;
	}
}
.container {
	width: 100%;
	height: 100vh;
	padding-bottom: 100rpx;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.foot {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: fixed;
	bottom: 0;
	padding-bottom: 20rpx;
	background-color: #f6f7fb;
}
.bottom {
	margin-top: 20rpx;
	width: calc(100% - 90rpx);
	display: flex;
	justify-content: center;
	align-items: center;

	.bottom-box-3 {
		width: calc(100% - 34rpx);
		height: 80rpx;
		background: #2168fe;
		box-shadow: 4rpx 8rpx 20rpx 2rpx rgba(33, 104, 254, 0.3);
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #ffffff;
		}
	}
}

.line {
	width: 2rpx;
	height: 28rpx;
	background: linear-gradient(180deg, rgba(123, 126, 154, 0) 0%, rgba(123, 126, 154, 0.5) 52%, rgba(123, 126, 154, 0) 100%);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
}
.main-top {
	width: calc(100% - 80rpx);
	height: 200rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
	.main-box {
		margin-top: 10rpx;
		width: calc(100% - 55rpx);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.box {
			width: 190rpx;
			height: 70rpx;
			background: #f5f7fa;
			border-radius: 10rpx 10rpx 10rpx 10rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 30rpx;
			color: #1f4085;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.active {
			background: #2168fe;
			color: #ffffff;
		}
	}
}
</style>
