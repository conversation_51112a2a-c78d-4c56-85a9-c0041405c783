<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="我的"
				title=" "
				:leftIconSize="0"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="userInfo">
				<view class="userInfo-left">
					<image
						v-if="user.headImage == ''"
						class="head"
						:src="imagebaseurl + 'static/icon/user_head.png'"
						mode=""></image>
					<image
						v-else
						class="head"
						:src="user.headImage"
						mode=""></image>
					<view class="userInfo-content">
						<view class="userInfo-content-top">
							<text class="userNmae">{{ user.nickName }}</text>
							<view class="role">
								<text>{{ user.masterType == 1 ? "学硕" : "专硕" }}</text>
							</view>
						</view>
						<view class="userInfo-content-bottom">
							<text>{{ schoolName }} | {{ professionNmae }}</text>

							<image
								:src="imagebaseurl + 'static/icon/v_icon.png'"
								mode=""></image>
						</view>
					</view>
				</view>
				<view
					class="seeting"
					@click="goSetting">
					<image
						:src="imagebaseurl + 'static/icon/setting.png'"
						mode=""></image>
					<text>设置</text>
				</view>
			</view>
			<view class="circle-progress-bg">
				<view class="circle-progress-box">
					<!-- 	<CircleProgress
						:percent="10"
						:width="150"
						inactiveColor="#DEE8F6"
						activeColor="#2168FE"
						borderWidth="12">
						<view class="circle-progress-content">
							<text class="text-1">{{ userStatisticsInfo.userSubjectCount }}</text>
							<text class="text-2">/</text>
							<text class="text-3">{{ userStatisticsInfo.totalSubjectCount }}</text>
						</view>
					</CircleProgress> -->
					<van-circle
						v-model="userStatisticsInfo.userSubjectCount / userStatisticsInfo.totalSubjectCount"
						:rate="100"
						:size="70"
						:speed="100"
						color="#2168FE"
						layer-color="#DEE8F6">
						<template #default>
							<view class="circle-progress-content">
								<text class="text-1">{{ userStatisticsInfo.userSubjectCount }}</text>
								<text class="text-2">/</text>
								<text class="text-3">{{ userStatisticsInfo.totalSubjectCount }}</text>
							</view>
						</template>
					</van-circle>
					<!-- <view class="circle-progress-content">
						<text class="text-1">{{ userStatisticsInfo.userSubjectCount }}</text>
						<text class="text-2">/</text>
						<text class="text-3">{{ userStatisticsInfo.totalSubjectCount }}</text>
					</view> -->
					<text class="circle-progress-box-title">刷题进度</text>
				</view>
				<view class="circle-progress-box">
					<!-- <CircleProgress
						:percent="10"
						:width="150"
						inactiveColor="#DEE8F6"
						activeColor="#FFB41F"
						borderWidth="12">
						<view class="circle-progress-content">
							<text
								class="text-1"
								style="color: #ffb41f"
								>{{ userStatisticsInfo.userLectureNotesCount }}</text
							>
							<text class="text-2">/</text>
							<text class="text-3">{{ userStatisticsInfo.totalLectureNotesCount }}</text>
						</view>
					</CircleProgress> -->
					<van-circle
						v-model="userStatisticsInfo.userLectureNotesCount / userStatisticsInfo.totalLectureNotesCount"
						:rate="100"
						:size="70"
						:speed="100"
						color="#ffb41f"
						layer-color="#DEE8F6">
						<template #default>
							<view class="circle-progress-content">
								<text
									class="text-1"
									style="color: #ffb41f"
									>{{ userStatisticsInfo.userLectureNotesCount }}</text
								>
								<text class="text-2">/</text>
								<text class="text-3">{{ userStatisticsInfo.totalLectureNotesCount }}</text>
							</view>
						</template>
					</van-circle>
					<!-- 	<view class="circle-progress-content">
						<text
							class="text-1"
							style="color: #ffb41f"
							>{{ userStatisticsInfo.userLectureNotesCount }}</text
						>
						<text class="text-2">/</text>
						<text class="text-3">{{ userStatisticsInfo.totalLectureNotesCount }}</text>
					</view> -->
					<text class="circle-progress-box-title">讲义背诵</text>
				</view>
				<view class="circle-progress-box">
					<!-- 	<CircleProgress
						:percent="10"
						:width="140"
						inactiveColor="#DEE8F6"
						activeColor="#1F4085"
						borderWidth="12">
						<view
							class="circle-progress-content"
							style="flex-direction: column">
							<text
								class="text-1"
								style="color: #1f4085"
								>{{ userStatisticsInfo.userStudyTime }}</text
							>
							<text
								class="text-2"
								style="font-weight: 400; font-size: 20rpx; color: #8590a7"
								>分钟</text
							>
						</view>
					</CircleProgress> -->
					<van-circle
						v-model="userStatisticsInfo.userStudyTime"
						:rate="100"
						:size="70"
						:speed="100"
						color="#1f4085"
						layer-color="#DEE8F6">
						<template #default>
							<view
								class="circle-progress-content"
								style="flex-direction: column">
								<text
									class="text-1"
									style="color: #1f4085"
									>{{ userStatisticsInfo.userStudyTime }}</text
								>
								<text
									class="text-2"
									style="font-weight: 400; font-size: 20rpx; color: #8590a7"
									>{{ userStatisticsInfo.timeUnit == "MINUTE" ? "分钟" : "小时" }}</text
								>
							</view>
						</template>
					</van-circle>
					<!-- <view
						class="circle-progress-content"
						style="flex-direction: column">
						<text
							class="text-1"
							style="color: #1f4085"
							>{{ userStatisticsInfo.userStudyTime }}</text
						>
						<text
							class="text-2"
							style="font-weight: 400; font-size: 20rpx; color: #8590a7"
							>分钟</text
						>
					</view> -->
					<text class="circle-progress-box-title">学习时长</text>
				</view>
			</view>

			<view class="menu">
				<text class="menu-title">功能专区</text>
				<view class="menu-item-box">
					<view
						class="menu-item"
						v-for="(item, index) in menu"
						:key="index"
						@click="goMenItem(item)">
						<image
							class="icon-left"
							:src="item.src"
							mode=""></image>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</view>

			<view class="foot">
				<view
					class="foot-item-box"
					@click="goHelp">
					<view class="foot-item">
						<image
							:src="imagebaseurl + 'static/icon/user_icon_1.png'"
							mode=""></image>
						<text>帮助反馈</text>
					</view>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
				<view
					class="foot-item-box"
					@click="goAboutUs()">
					<view class="foot-item">
						<image
							:src="imagebaseurl + 'static/icon/user_icon_2.png'"
							mode=""></image>
						<text>关于我们</text>
					</view>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</view>
				<!-- <view class="foot-item-box"> -->
				<button
					class="foot-item-box"
					@click="jumpToWeChatCustomerService">
					<view class="foot-item">
						<image
							:src="imagebaseurl + 'static/icon/user_icon_3.png'"
							mode=""></image>
						<text>联系我们</text>
					</view>
					<image
						:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
						mode=""></image>
				</button>
				<!-- </view> -->
			</view>
		</view>
	</view>
</template>

<script>
import CircleProgress from "../../component/comment/CircleProgress.vue";
import { imagebaseurl } from "../../api/index.js";
import { getUserStatistics, getSchoolList, getProfessionList, goLogin, getWechatConfig, getUserInfo, phoneLogin } from "../../api/api.js";
import { mapState } from "vuex";
export default {
	components: { CircleProgress },
	computed: {
		...mapState({
			openId: state => state.openId, // 从 Vuex 的 state 中提取 openId
		}),
	},
	data() {
		return {
			percent: 60,
			topHeight: 0,
			bgColor: "rgba(255,255,255,0)",
			imagebaseurl: imagebaseurl,
			menu: [
				// { id: 1, name: '立格商城', src: imagebaseurl + 'static/icon/menu_item_3.png' },
				{ id: 2, name: "我的订单", src: imagebaseurl + "static/icon/menu_item_10.png", page: "/pages/order/orderList" },
				// { id: 3, name: '在线数据统计', src: imagebaseurl + 'static/icon/menu_item_7.png' },
				{ id: 4, name: "真题库", src: imagebaseurl + "static/icon/menu_item_6.png", page: "/pages/realQuestionBank/realQuestionBank" },
				{ id: 5, name: "我的课程", src: imagebaseurl + "static/icon/menu_item_5.png", page: "/pages/courses/myCourses" },
				// { id: 6, name: '我的收藏', src: imagebaseurl + 'static/icon/menu_item_8.png', page: '/pages/userPage/myLectureNotes' },
				{ id: 7, name: "我的讲义", src: imagebaseurl + "static/icon/menu_item_9.png", page: "/pages/userPage/userHandout" },
			],
			user: uni.getStorageSync("user"),
			userStatisticsInfo: {},
			schoolName: "",
			professionNmae: "",
			openid: uni.getStorageSync("openId"),
			companyId: "",
			customerUrl: "",
			currentRate: 1000,
		};
	},
	onLoad() {
		const token = uni.getStorageSync("token");
		if (token == null || token == undefined || token == "") {
			uni.showToast({
				title: "登录已失效，即将前往登录",
				icon: "none",
				success() {
					setTimeout(() => {
						uni.reLaunch({
							url: "/pages/login/phoneLogin",
						});
					}, 500);
				},
			});
			return;
		}
		//获取开课的全部学校
		this.userStatistics();
		getSchoolList({ masterType: this.user.masterType, isOpen: 1 })
			.then(res => {
				if (res.result == "1") {
					this.schoolList = res.data;
					this.schoolName = this.schoolList.find(item => item.id == this.user.globalSchoolId).school;
				} else {
					this.$showToast(res.message || " 查询失败，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || " 查询失败，请重试");
			});
		getProfessionList({ schoolId: this.user.globalSchoolId })
			.then(res => {
				if (res.result == "1") {
					this.professionList = res.data;
					this.professionNmae = this.professionList.find(item => item.id == this.user.globalProfessionId).schoolProfession;
				} else {
					this.$showToast(res.message || " 查询失败，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || " 查询失败，请重试");
			});
	},
	mounted() {
		this.getElementHeight();
		//获取微信客服配置
		getWechatConfig({
			masterType: this.user.masterType,
			customerType: 2,
		})
			.then(res => {
				if (res.result == "1") {
					this.companyId = res.data.companyId;
					this.customerUrl = res.data.customerUrl;
				} else {
					this.$showToast(res.message || " 客服配置异常，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || " 客服配置异常，请重试");
			});
	},
	onShow() {
		this.getUser();
	},
	beforeDestroy() {
		// 页面隐藏时注销监听
	},
	methods: {
		getUser() {
			getUserInfo({
				masterType: this.user.masterType,
				globalSchoolId: this.user.globalSchoolId,
				openId: this.openid,
			})
				.then(res => {
					if (res.result == "1") {
						this.user = res.data;
						uni.removeStorageSync("user");
						uni.setStorageSync("user", res.data);
					} else {
						this.$showToast(res.message || "获取用户信息失败");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取用户信息失败");
				});
		},
		// 跳转微信客服
		jumpToWeChatCustomerService() {
			this.openWeChatCustomerService(this.customerUrl, this.companyId);
		}, // 打开微信客服
		openWeChatCustomerService(
			weiXinCustomerServiceUrl = "",
			corpId = "",
			showMessageCard = false,
			sendMessageTitle = "",
			sendMessagePath = "",
			sendMessageImg = ""
		) {
			if (!weiXinCustomerServiceUrl || !corpId) return this.$showToast("请配置好客服链接或者企业ID"); // eslint-disable-next-line no-undef
			wx.openCustomerServiceChat({
				// 客服信息
				extInfo: {
					url: weiXinCustomerServiceUrl, // 客服链接 https://work.weixin.qq.com/xxxxxxxx
				},
				corpId, // 企业ID wwed1ca4d3597eXXXX
				showMessageCard, // 是否发送小程序气泡消息
				sendMessageTitle, // 气泡消息标题
				sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
				sendMessageImg, // 气泡消息图片
				success(res) {
					console.log("success", JSON.stringify(res));
				},
				fail(err) {
					console.log("fail", JSON.stringify(err));
					// eslint-disable-next-line no-undef
					return wx.showToast({
						title: err.errMsg,
						icon: "none",
					});
				},
			});
		},
		// getUser(openId) {
		// 	goLogin({ openId: openId })
		// 		.then(res => {
		// 			if (res.result == "1") {
		// 				this.user = res.data;
		// 				uni.setStorageSync("user", res.data);
		// 				uni.setStorageSync("token", res.data.token);
		// 				this.userStatistics();
		// 			} else {
		// 				this.$showToast(res.message || "查询失败，请重试");
		// 			}
		// 		})
		// 		.catch(err => {
		// 			this.$showToast(err.message || "查询失败，请重试");
		// 		});
		// },
		userStatistics() {
			getUserStatistics({
				masterType: this.user.masterType,
			})
				.then(res => {
					if (res.result == "1") {
						this.userStatisticsInfo = res.data;
					} else {
						this.$showToast(res.message || "查询失败，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询失败，请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
		goSetting() {
			uni.navigateTo({
				url: "/pages/user/userSetting",
			});
		},
		goHelp() {
			uni.navigateTo({
				url: "/pages/userPage/helpWithFeedback",
			});
		},
		goMenItem(item) {
			if (item.page) {
				if (item.id == 5) {
					uni.switchTab({
						url: item.page,
					});
				} else {
					uni.navigateTo({
						url: item.page,
					});
				}
			}
		},
		goAboutUs() {
			console.log(111);
			uni.navigateTo({
				url: "/pages/aboutUs/aboutUs",
			});
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}
.container {
	width: 100%;
	min-height: 100vh;
	padding-bottom: 100rpx;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 291%);
	position: fixed;
	top: 0;
	z-index: 9999999;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.main {
	width: 100%;

	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 9;
}
.userInfo {
	width: calc(100% - 60rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	.userInfo-left {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 30rpx;
		.head {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
		}
		.userInfo-content {
			display: flex;
			flex-direction: column;
			.userInfo-content-top {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 10rpx;
				.userName {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #1f4085;
				}
				.role {
					width: 58rpx;
					height: 32rpx;
					background: #2168fe;
					border-radius: 6rpx 6rpx 6rpx 6rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					font-weight: bold;
					font-size: 20rpx;
					color: #ffffff;
				}
			}
			.userInfo-content-bottom {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 10rpx;
				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 26rpx;
					color: #1f4085;
				}
				image {
					width: 22rpx;
					height: 24rpx;
				}
			}
		}
	}
	.seeting {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 10rpx;
		image {
			width: 27.05rpx;
			height: 29.76rpx;
		}
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 26rpx;
			color: #8590a7;
		}
	}
}
.circle-progress-bg {
	margin-top: 30rpx;
	width: calc(100% - 60rpx);
	padding-top: 20rpx;
	padding-bottom: 20rpx;
	height: 200rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	overflow: hidden;
	display: flex;
	justify-content: space-around;
	align-items: center;
	.circle-progress-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 15rpx;
		.circle-progress-box-title {
			font-weight: 400;
			font-size: 24rpx;
			color: #1f2638;
		}
		.circle-progress-content {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 3rpx;
			.text-1 {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 37rpx;
				color: #2168fe;
			}
			.text-2 {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 24rpx;
				color: #708aba;
			}
			.text-3 {
				font-family: DINPro, DINPro;
				font-weight: bold;
				font-size: 24rpx;
				color: #708aba;
				position: relative;
				top: 4rpx;
			}
		}
	}
}
.menu {
	margin-top: 20rpx;
	width: calc(100% - 60rpx);
	padding-bottom: 30rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.menu-title {
		padding: 20rpx;
		width: calc(100% - 60rpx);
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 32rpx;
		color: #1f2638;
	}
	.menu-item-box {
		margin-top: 20rpx;
		width: calc(100% - 76rpx);
		display: flex;
		// flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		// gap: 67rpx;

		.menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10rpx;
			image {
				width: 80rpx;
				height: 80rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #1f2638;
			}
		}
	}
}
.foot {
	margin-top: 20rpx;
	padding-top: 24rpx;
	padding-bottom: 24rpx;
	width: calc(100% - 60rpx);
	height: 360rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	align-items: center;

	.foot-item-box {
		width: calc(100% - 60rpx);
		height: 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0;
		background: none;
		.foot-item {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;
			image {
				width: 36.13rpx;
				height: 36.13rpx;
			}
			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
		}
		image {
			width: 10.65rpx;
			height: 18.63rpx;
		}
	}
	.foot-item-box::after {
		border: none;
	}
}
</style>
