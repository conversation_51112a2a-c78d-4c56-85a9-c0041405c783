<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="设置"
				leftIconColor="#212838"
				autoBack
				title=" "
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
		</view>
		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="main-card">
				<view
					class="main-box"
					style="margin-top: 60rpx">
					<text>头像</text>
					<view class="main-box-right">
						<view class="upload">
							<!-- <u-upload name="1" multiple :maxCount="10"><image class="head" :src="imagebaseurl + 'static/icon/user_head.png'" mode=""></image></u-upload> -->
							<image
								v-if="currFiles.length == 0 && user.headImage == ''"
								class="head"
								:src="imagebaseurl + 'static/icon/user_head.png'"
								@click="showMediaAlert" />
							<image
								v-if="currFiles.length == 0 && user.headImage != ''"
								class="head"
								:src="user.headImage"
								@click="showMediaAlert" />
							<image
								v-if="currFiles.length > 0"
								class="head"
								:src="currFiles[0]"
								@click="showMediaAlert" />
						</view>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
				<view class="main-box">
					<text>姓名</text>
					<view class="main-box-right">
						<u--input
							border="none"
							v-model="user.nickName"
							inputAlign="right"
							color="#8590a7"></u--input>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
				<!-- 	<view class="main-box" @click="open(1)">
					<text>性别</text>
					<view class="main-box-right">
						<text>女</text>
						<image class="icon" :src="imagebaseurl + 'static/icon/arrow_right_icon.png'" mode=""></image>
					</view>
				</view> -->
				<view class="main-box">
					<text>硕士类型</text>
					<view
						class="main-box-right"
						@click="open(2)">
						<text>{{ user.masterType == 1 ? "学硕" : "专硕" }}</text>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>

				<view class="main-box">
					<text>报考学校</text>
					<view
						class="main-box-right"
						@click="open(3)">
						<text>{{ schoolName }}</text>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
				<view class="main-box">
					<text>拟报专业</text>
					<view
						class="main-box-right"
						@click="open(4)">
						<text>{{ professionNmae }}</text>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
				<view class="main-box">
					<text>联系方式</text>
					<view class="main-box-right">
						<!-- <text>173 1690 3680</text> -->
						<u--input
							readonly
							border="none"
							v-model="user.phone"
							inputAlign="right"
							color="#8590a7"></u--input>
						<image
							class="icon"
							:src="imagebaseurl + 'static/icon/arrow_right_icon.png'"
							mode=""></image>
					</view>
				</view>
			</view>
			<view
				class="btn-1"
				@click="submit">
				<text>确认</text>
			</view>
			<view
				class="btn-2"
				@click="exit">
				<text>退出登录</text>
			</view>
		</view>
		<van-action-sheet
			:show="showSheet"
			:actions="actions"
			cancel-text="取消"
			close-on-click-action
			@select="onSelect"
			@cancel="onCancel" />
		<view
			class=""
			v-if="show">
			<u-picker
				:show="show"
				:columns="columns"
				:keyName="keyName"
				:defaultIndex="[defaultIndex]"
				@cancel="show = false"
				@confirm="confirm"></u-picker>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { uploadFile, getSchoolList, updateUser, getProfessionList, getUploadConfigUrl, getFileUrl, phoneLogin, getUserInfo } from "../../api/api";
import { initQiniu, qiniuUploader } from "../../api/qiniu_index.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			imagebaseurl: imagebaseurl,
			currFiles: [],
			showSheet: false,
			actions: [{ name: "相册选择" }, { name: "相机" }],
			userImages: [],
			userImageKeys: "",
			user: uni.getStorageSync("user"),
			show: false,
			columns: [[]],
			openIndex: 0,
			sexItem: {},
			masterType: {},
			schoolList: [],
			schoolItem: {},
			schoolName: "",
			keyName: "name",
			professionList: [],
			professionItem: {},
			professionNmae: "",
			uptoken: "",
			fileKey: "",
			openId: "",
			defaultIndex: 0,
		};
	},
	onLoad() {
		this.openId = this.$store.openId || uni.getStorageSync("openId");
		//获取开课的全部学校
		getSchoolList({ masterType: this.user.masterType, isOpen: 1 })
			.then(res => {
				if (res.result == "1") {
					this.schoolList = res.data;
					this.schoolItem = this.schoolList.find(item => item.id == this.user.globalSchoolId);
					this.schoolName = this.schoolItem.school || "请选择学校";
				} else {
					this.$showToast(res.message || " 查询失败，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || " 查询失败，请重试");
			});
		getProfessionList({ schoolId: this.user.globalSchoolId })
			.then(res => {
				if (res.result == "1") {
					this.professionList = res.data;
					this.professionItem = this.professionList.find(item => item.id == this.user.globalProfessionId);
					this.professionNmae = this.professionItem.schoolProfession || "请选择专业";
				} else {
					this.$showToast(res.message || " 查询失败，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || " 查询失败，请重试");
			});

		getUploadConfigUrl({ fileName: "userHead", type: "pic" })
			.then(res => {
				console.log("七牛云参数：", res);
				uni.setStorageSync("userHeadFileName", "userHead");
				this.fileKey = res.data.fileKey;
				this.uptoken = res.data.token;
				if (res.result == "1") {
				} else {
					this.$showToast(res.message || "查询失败，请重试");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "查询失败，请重试");
			});
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		exit() {
			uni.reLaunch({
				url: "/pages/login/phoneLogin",
			});
		},
		submit() {
			if (Object.keys(this.schoolItem).length == 0) {
				this.$showToast("请选择学校");
				return;
			}
			if (Object.keys(this.professionItem).length == 0) {
				this.$showToast("请选择专业");
				return;
			}
			let params = {
				headImage: "",
				nickName: this.user.nickName,
				changeSchoolId: this.schoolItem.id || this.user.globalSchoolId,
				masterType: this.masterType.id || this.user.masterType,
				changeProfessionId: this.professionItem.id || this.user.globalProfessionId,
			};
			if (this.userImageKeys == "") {
				params.headImage = this.user.headImage;
			} else {
				params.headImage = this.userImageKeys;
			}
			let that = this;
			uni.showModal({
				title: "提示",
				content: `修改学校之后您在小程序内访问的数据都将切换至《${that.schoolName}-${that.professionNmae}》，确认修改吗？`,
				cancelColor: "#c5c5c5",
				success(res) {
					if (res.confirm) {
						updateUser(params).then(res => {
							if (res.result == "1") {
								// that.$showToast("修改成功");
								uni.showModal({
									title: "提示",
									content: `修改成功，请重新登录`,
									cancelColor: "#c5c5c5",
									showCancel: false,
									success(res) {
										if (res.confirm) {
											uni.reLaunch({
												url: "/pages/login/phoneLogin",
											});
										}
									}
								})

								// phoneLogin({ openId: that.openId })
								// 	.then(res => {
								// 		if (res.result == "1") {
								// 			uni.removeStorageSync("openId");
								// 			uni.removeStorageSync("user");
								// 			uni.removeStorageSync("token");
								// 			// uni.setStorageSync("openId", that.openId);
								// 			uni.setStorageSync("openId", that.openId);

								// 			uni.setStorageSync("user", res.data);

								// 			uni.setStorageSync("token", res.data.token);
								// 			uni.reLaunch({
								// 				url: "/pages/home/<USER>",
								// 			});
								// 		} else {
								// 			that.$showToast(res.mssage || "修改失败");
								// 		}
								// 	})
								// 	.catch(err => {
								// 		that.$showToast(err.message || "修改失败");
								// 	});

								// setTimeout(() => {
								// 	uni.navigateBack({
								// 		delta: 1,
								// 		success: function () {
								// 			// 通知所有监听onBackUser事件的页面执行相应的操作
								// 			uni.$emit("onBackUser", { msg: true });
								// 		},
								// 	});
								// }, 1000);
							}
						});
					}
				},
			});
		},
		open(index) {
			// if (index == 1) {
			// 	this.openIndex = index;
			// 	this.columns = [
			// 		[
			// 			{ id: 1, name: '男' },
			// 			{ id: 2, name: '女' }
			// 		]
			// 	];
			// }
			this.openIndex = index;
			if (index == 2) {
				this.keyName = "name";
				this.columns = [
					[
						{ id: 1, name: "学硕" },
						{ id: 2, name: "专硕" },
					],
				];
				this.defaultIndex = this.columns[0].findIndex(item => item.id == this.user.masterType);
				this.defaultIndex < 0 ? (this.defaultIndex = 0) : "";
			}
			if (index == 3) {
				this.keyName = "school";
				this.columns[0] = this.schoolList;
				this.defaultIndex = this.schoolList.findIndex(item => item.id == this.schoolItem.id);
				this.defaultIndex < 0 ? (this.defaultIndex = 0) : "";
			}
			if (index == 4) {
				if (this.professionList.length == 0) {
					this.$showToast("暂无拟报专业数据");
					return;
				}
				this.columns[0] = this.professionList;
				this.keyName = "schoolProfession";
				this.defaultIndex = this.professionList.findIndex(item => item.id == this.professionItem.id);
				this.defaultIndex < 0 ? (this.defaultIndex = 0) : "";
			}
			this.show = true;
		},
		confirm(e) {
			console.log(e);
			if (this.openIndex == 1) {
				this.sexItem = e.value[0];
			}
			if (this.openIndex == 2) {
				this.masterType = e.value[0];
				this.user.masterType = this.masterType.id;
				this.keyName = "school";
				this.schoolItem = {};
				this.schoolName = "请选择学校";
				this.professionItem = {};
				this.professionNmae = "请选择拟报专业";
				getSchoolList({ masterType: this.masterType.id }).then(res => {
					this.schoolList = res.data;
				});
			}
			if (this.openIndex == 3) {
				this.schoolItem = e.value[0];
				this.schoolName = this.schoolItem.school;
				this.professionItem = {};
				this.professionNmae = "请选择拟报专业";
				getProfessionList({ schoolId: this.schoolItem.id }).then(res => {
					this.professionList = res.data;
					if (this.professionList.length == 0) {
						this.$showToast("暂无拟报专业数据");
					}
				});
			}
			if (this.openIndex == 4) {
				this.professionItem = e.value[0];
				this.professionNmae = this.professionItem.schoolProfession;
			}
			this.show = false;
		},
		showMediaAlert() {
			this.showSheet = true;
		},
		onCancel() {
			this.showSheet = false;
		},
		onSelect(item) {
			let self = this;
			if (item.detail.name == "相册选择") {
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["album"], //从相册选择
					success: function (res) {
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			} else if (item.detail.name == "相机") {
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["camera"], //从相册选择
					success: function (res) {
						console.log(res);
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			}
			this.showSheet = false;
		},

		uploadFileResp(tempFilePaths) {
			let self = this;
			var filePath = tempFilePaths[0];
			this.userImages = this.currFiles;
			initQiniu({ uptoken: this.uptoken });
			// 向七牛云上传
			qiniuUploader.upload(
				filePath,
				res => {
					const result = JSON.parse(JSON.stringify(res));
					getFileUrl({ fileKey: result.key, type: "pic" }).then(resFile => {
						this.currFiles = this.currFiles.concat(resFile.data);
						this.userImageKeys = result.key;
						if (resFile.result == "1") {
						} else {
							self.$showToast(resFile.message, "上传失败,请重试");
						}
					});
				},
				error => {
					console.error("error: " + JSON.stringify(error));
					self.$showToast(JSON.stringify(error), "上传失败,请重试");
				},
				{
					region: "NCN", // 华北区
					uptokenURL: "",
					domain: "",
					shouldUseQiniuFileName: false,
					key: this.fileKey,
					uptokenURL: "",
				}
			);
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
.container {
	width: 100%;
	height: 100vh;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	background: linear-gradient(180deg, #bbddfa 2%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
}
.main {
	margin-top: 20rpx;
	width: 100%;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	.main-card {
		width: calc(100% - 60rpx);
		height: 780rpx;
		background: #ffffff;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.main-box {
			margin-top: 72rpx;
			width: calc(100% - 60rpx);
			display: flex;
			justify-content: space-between;
			align-items: center;
			text {
				width: 128rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
			}
			.main-box-right {
				width: calc(100% - 128rpx);
				display: flex;
				justify-content: flex-end;
				align-items: center;
				gap: 20rpx;
				text {
					width: auto;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #8590a7;
				}
				.upload {
					.head {
						width: 70rpx;
						height: 70rpx;
						border-radius: 50%;
					}
				}
				.icon {
					width: 10.65rpx;
					height: 18.63rpx;
				}
			}
		}
	}
}
.btn-1 {
	margin-top: 80rpx;
	width: 400rpx;
	height: 98rpx;
	background: #2168fe;
	border-radius: 50rpx 50rpx 50rpx 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 500;
	font-size: 36rpx;
	color: #ffffff;
}
.btn-2 {
	margin-top: 80rpx;
	width: 400rpx;
	height: 100rpx;
	background: #e5e7ef;
	border-radius: 50rpx 50rpx 50rpx 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 32rpx;
	color: #8590a7;
}
</style>
