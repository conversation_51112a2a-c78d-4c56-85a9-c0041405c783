/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-25 13:39:44
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2022-09-25 13:41:39
 * @FilePath: /funds_approve_mobile/src/component/plugins.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'

import NavBar from "./NavBar";
import NavContent from "./NavContent";

function CustomPlugins(Vue) {
    Vue.component("nav-bar", NavBar);
    Vue.component("nav-content", NavContent);
}

export default CustomPlugins;