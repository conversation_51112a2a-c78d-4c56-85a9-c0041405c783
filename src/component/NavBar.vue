<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-25 09:01:23
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2022-10-07 12:01:41
 * @FilePath: /funds_approve_mobile/src/component/NavBar.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <view class="nav-bar">
        <van-nav-bar
            :title="title"
            :left-arrow="leftArrow"
            @click-left="onClickLeft"
        />
    </view>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        leftArrow: {
            type: Boolean,
            default: true
        },
    },
    methods: {
        onClickLeft() {
            this.$eGoBack();
        }
    }
}
</script>

<style lang="scss" scoped>
.nav-bar {
    width: 100vw;
    position: fixed;
    top: 0rpx;
    height: 92rpx;
    z-index: 99;
}
::v-deep .van-nav-bar .van-icon {
    color: #272727;
}
</style>