<template>
	<view class="message-container" v-show="toastShow">
		<view :class="toast == true ? 'message-card fade-in-fwd' : 'message-card fade-out'">
			<image src="../../static/icon/answer_correct.png" mode=""></image>
			<text>{{ title }}</text>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: '取消成功'
		},
		show: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			toastShow: false,
			toast: false
		};
	},
	watch: {
		show(newVal) {
			this.toastShow = true;
			this.toast = true;
			setTimeout(() => {
				this.toast = false;
				setTimeout(() => {
					this.toastShow = false;
				}, 1000);
			}, 1000);
		}
	}
};
</script>

<style lang="scss">
.message-container {
	// width: 100%;
	// display: flex;
	// justify-content: center;
	// align-items: center;
}
.message-card {
	width: 248rpx;
	height: 72rpx;
	background: linear-gradient(94deg, #d4fee5 0%, rgba(255, 255, 255, 0) 100%);
	border-radius: 40rpx 40rpx 40rpx 40rpx;
	border: 2rpx solid rgba(35, 195, 98, 0.48);
	position: absolute;
	top: 296rpx;
	left: 255rpx;
	// left: 50%;
	z-index: 9999;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 10rpx;
	image {
		margin-left: 20rpx;
		width: 52rpx;
		height: 52rpx;
	}
	text {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 32rpx;
		color: #23c362;
	}
}
.fade-in-fwd {
	-webkit-animation: fade-in-fwd 1s cubic-bezier(0.39, 0.575, 0.565, 1) both;
	animation: fade-in-fwd 1s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2025-1-3 18:10:24
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-in-fwd
 * ----------------------------------------
 */
@-webkit-keyframes fade-in-fwd {
	0% {
		-webkit-transform: translateZ(-80px);
		transform: translateZ(-80px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
}
@keyframes fade-in-fwd {
	0% {
		-webkit-transform: translateZ(-80px);
		transform: translateZ(-80px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
}
.fade-out {
	-webkit-animation: fade-out 1s ease-out both;
	animation: fade-out 1s ease-out both;
}
/* ----------------------------------------------
 * Generated by Animista on 2025-1-3 18:18:32
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-out
 * ----------------------------------------
 */
@-webkit-keyframes fade-out {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}
@keyframes fade-out {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}
</style>
