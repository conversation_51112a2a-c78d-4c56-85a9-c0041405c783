<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-10-01 18:15:27
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2022-12-15 22:18:46
 * @FilePath: /funds_approve_mobile/src/component/comment/UploadFile.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<view class="back-view">
		<view
			class="image-view"
			v-for="(item, index) in currFiles"
			:key="index">
			<image
				class="image"
				:src="item"
				@click="previewImage(currFiles, index)" />
			<image
				v-if="isEdit"
				src="../../static/icon/btn_delet.png"
				class="btn_delet"
				@click="deletImageClick(item)" />
		</view>
		<image
			v-if="maxCount > currFiles.length && isEdit"
			class="image-icon"
			src="../../static/icon/upload.png"
			@click="showMediaAlert" />

		<van-action-sheet
			:show="showSheet"
			:actions="actions"
			cancel-text="取消"
			close-on-click-action
			@select="onSelect"
			@cancel="onCancel" />
	</view>
</template>

<script>
import { uploadFile, getUploadConfigUrl, getFileUrl } from "../../api/api";
import { initQiniu, qiniuUploader } from "../../api/qiniu_index.js";
export default {
	props: {
		fileList: {
			type: Array,
			default: [],
		},
		isEdit: {
			type: Boolean,
			default: true,
		},
		maxCount: {
			type: Number,
			default: 1000,
		},
		uptoken: {
			type: String,
			default: "",
		},
		fileKey: {
			type: String,
			default: "",
		},
	},

	data() {
		return {
			showSheet: false,
			actions: [{ name: "相册选择" }, { name: "相机" }],
			currFiles: this.fileList,
		};
	},

	watch: {
		fileList(newVal, oldVal) {
			this.currFiles = newVal;
		},
	},

	methods: {
		showMediaAlert() {
			this.showSheet = true;
		},
		onCancel() {
			this.showSheet = false;
		},
		onSelect(item) {
			let self = this;
			if (item.detail.name == "相册选择") {
				uni.chooseImage({
					count: 9, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["album"], //从相册选择
					success: function (res) {
						console.log(res);
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			} else if (item.detail.name == "相机") {
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ["camera"], //从相册选择
					success: function (res) {
						self.uploadFileResp(res.tempFilePaths);
					},
				});
			}
			this.showSheet = false;
		},
		uploadFileResp(tempFilePaths) {
			var filePath = tempFilePaths[0];
			this.userImages = this.currFiles;
			initQiniu({ uptoken: this.uptoken });
			// 向七牛云上传
			qiniuUploader.upload(
				filePath,
				res => {
					const result = JSON.parse(JSON.stringify(res));
					getFileUrl({ fileKey: result.key, type: "pic" }).then(resFile => {
						this.currFiles = this.currFiles.concat(resFile.data);
						this.userImageKeys = result.key;
						this.$emit("uploadSuccess", {
							keys: [result.key],
							filesUrl: this.currFiles,
						});
					});
				},
				error => {
					console.error("error: " + JSON.stringify(error));
				},
				{
					region: "NCN", // 华北区
					uptokenURL: "",
					domain: "",
					shouldUseQiniuFileName: false,
					key: this.fileKey,
					uptokenURL: "",
				}
			);
		},
		// uploadFileResp(tempFilePaths, tempFiles) {
		// 	uploadFile({
		// 		fileName: 'file',
		// 		filePath: tempFiles[0].path,
		// 		formData: {
		// 			file: tempFiles[0].path,
		// 			type: 'pic'
		// 		}
		// 	})
		// 		.then((res) => {
		// 			let result = JSON.parse(res);
		// 			if (result.result == '1') {
		// 				this.currFiles = this.currFiles.concat(tempFilePaths);
		// 				this.$emit('uploadSuccess', {
		// 					keys: [result.data],
		// 					filesUrl: this.currFiles
		// 				});
		// 			} else {
		// 				this.$showToast(result.message || '上传失败，请重试');
		// 			}
		// 		})
		// 		.catch((err) => {
		// 			this.$showToast(err || '上传失败，请重试');
		// 		});
		// },

		deletImageClick(file) {
			this.$emit("deletImage");
		},

		previewImage(currFiles, index) {
			// 预览图片
			uni.previewImage({
				urls: currFiles,
				current: index,
				longPressActions: {
					itemList: [],
					success: function (data) {
						console.log("选中了第" + (data.tapIndex + 1) + "个按钮,第" + (data.index + 1) + "张图片");
					},
					fail: function (err) {
						console.log(err.errMsg);
					},
				},
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.back-view {
	width: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.image-view {
	position: relative;
}
.image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 8rpx;
	margin-right: 15rpx;
	margin-bottom: 15rpx;
}
.image-icon {
	width: 60.75rpx;
	height: 58.39rpx;
}
.btn_delet {
	height: 44rpx;
	width: 44rpx;
	padding: 10rpx;
	position: absolute;
	top: -20rpx;
	right: 0rpx;
}
</style>
