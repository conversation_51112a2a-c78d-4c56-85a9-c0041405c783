<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-25 09:02:10
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2022-09-25 09:16:52
 * @FilePath: /funds_approve_mobile/src/component/NavContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <view class="navContent">
        <slot></slot>
    </view>
</template>

<script>
export default {
    
}
</script>

<style lang="scss" scoped>
.navContent {
    width: 100%;
    overflow: scroll;
    margin-top: 92rpx;
}
</style>