/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-13 23:38:44
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-28 23:02:46
 * @FilePath: /funds_approve_mobile/src/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import App from './App';

import store from './store';
import {
	showToast
} from './utils/utils';

import CustomPlugins from './component/CustomPlugins';
// main.js
import uView from 'uview-ui';

Vue.config.productionTip = false;

Vue.prototype.$store = store;
Vue.prototype.$showToast = showToast;

Vue.prototype.$VERSION = '3.0.4';

Vue.prototype.$resolvePrivacyAuthorization = null;

Vue.use(CustomPlugins);
Vue.use(uView);

App.mpType = 'app';

const app = new Vue({
	...App,
});
app.$mount();