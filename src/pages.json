{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/phoneLogin",
			"style": {
				"navigationBarTitleText": "手机号登录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "信息填写"
			}
		},
		{
			"path": "pages/score/index",
			"style": {
				"navigationBarTitleText": "成绩排行"
			}
		},
		{
			"path": "pages/privacy/index",
			"style": {
				"navigationBarTitleText": "隐私协议"
			}
		}, {
			"path": "pages/lectureNotes/lectureNotes",
			"style": {
				"navigationBarTitleText": "讲义背诵",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/volunteerRanking/volunteerRanking",
			"style": {
				"navigationBarTitleText": "志愿/排名",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/shopping/shopping",
			"style": {
				"navigationBarTitleText": "商城",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/lectureNotes/lectureNotesDeatil",
			"style": {
				"navigationBarTitleText": "讲义背诵详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/user/userSetting",
			"style": {
				"navigationBarTitleText": "设置",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/register/register",
			"style": {
				"navigationBarTitleText": "注册",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/courses/myCourses",
			"style": {
				"navigationBarTitleText": "我的课程",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/scoreRanking/examRankingQuery",
			"style": {
				"navigationBarTitleText": "研究生考试排名查询",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/scoreRanking/scoreRanking",
			"style": {
				"navigationBarTitleText": "成绩排行",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/scoreRanking/professionalRanking",
			"style": {
				"navigationBarTitleText": "专业排名",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/previousAnnouncements/previousAnnouncements",
			"style": {
				"navigationBarTitleText": "往期公告",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/webview/webview",
			"style": {
				"navigationBarTitleText": " 立格致远教育",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/volunteerRanking/volunteerApplicationStatistics",
			"style": {
				"navigationBarTitleText": "志愿填报统计",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/volunteerRanking/volunteerStatistics",
			"style": {
				"navigationBarTitleText": "志愿统计",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/volunteerRanking/graspingState",
			"style": {
				"navigationBarTitleText": "掌握情况",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/realQuestionBank/realQuestionBank",
			"style": {
				"navigationBarTitleText": "真题库",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/realQuestionBank/testQuestions",
			"style": {
				"navigationBarTitleText": "真题库试题",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userPage/helpWithFeedback",
			"style": {
				"navigationBarTitleText": "帮助反馈",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userPage/userHandout",
			"style": {
				"navigationBarTitleText": "我的讲义",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/userPage/myLectureNotes",
			"style": {
				"navigationBarTitleText": "我的讲义",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/aboutUs/aboutUs",
			"style": {
				"navigationBarTitleText": "关于我们",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/order/orderList",
			"style": {
				"navigationBarTitleText": "我的订单",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/order/orderPay",
			"style": {
				"navigationBarTitleText": "订单支付",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/userPage/myCoursesPlay",
			"style": {
				"navigationBarTitleText": "课程视频",
				"navigationStyle": "custom"
			}
		}

	],
	"tabBar": {
		"borderStyle": "white",
		"color": "#000",
		"selectedColor": "#2168FE",
		"backgroundColor": "#ffffff",

		"list": [{
				"pagePath": "pages/home/<USER>",
				"text": "首页",
				"iconPath": "static/icon/home.png",
				"selectedIconPath": "static/icon/select_home.png"
			},
			{
				"pagePath": "pages/lectureNotes/lectureNotes",
				"text": "讲义背诵",
				"iconPath": "static/icon/lectureNotes.png",
				"selectedIconPath": "static/icon/select_lectureNotes.png"
			},
			{
				"pagePath": "pages/volunteerRanking/volunteerRanking",
				"text": "志愿/排名",
				"iconPath": "static/icon/volunteerRanking.png",
				"selectedIconPath": "static/icon/select_volunteerRanking.png"
			},
			{
				"pagePath": "pages/courses/myCourses",
				"text": "我的课程",
				"iconPath": "static/icon/shopping.png",
				"selectedIconPath": "static/icon/select_shopping.png"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的",
				"iconPath": "static/icon/user.png",
				"selectedIconPath": "static/icon/select_user.png"

			}
		]
	},
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"usingComponents": {
			"van-image": "/wxcomponents/vant/image/index",
			"van-button": "/wxcomponents/vant/button/index",
			"van-cell": "/wxcomponents/vant/cell/index",
			"van-cell-group": "/wxcomponents/vant/cell-group/index",
			"van-field": "/wxcomponents/vant/field/index",
			"van-radio": "/wxcomponents/vant/radio/index",
			"van-radio-group": "/wxcomponents/vant/radio-group/index",
			"van-picker": "/wxcomponents/vant/picker/index",
			"van-popup": "/wxcomponents/vant/popup/index",
			"van-icon": "/wxcomponents/vant/icon/index",
			"van-action-sheet": "/wxcomponents/vant/action-sheet/index",
			"van-circle": "/wxcomponents/vant/circle/index"

		}
	},
	"subPackages": [

	]
}