<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-13 23:38:44
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-02-10 22:26:24
 * @FilePath: /funds_approve_mobile/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script>
import Vue from "vue";
import { getVersion } from "./api/api";
export default {
	onLaunch: function (option) {
		console.log("App Launch");
		if (option.scene == 1154 || option.scene == 1155) {
			console.log(option);
			console.log(option.path == "pages/volunteerRanking/volunteerStatistics");
			if (option.path == "pages/volunteerRanking/volunteerStatistics") {
				uni.showModal({
					title: "提示",
					content: "请前往小程序查看更多内容",
					confirmText: "确定",
					success: function (res) {
						if (res.confirm) {
							uni.reLaunch({
								url: "/pages/volunteerRanking/volunteerApplicationStatistics",
							});
						}
					},
				});
			}
		}

		if (wx.onNeedPrivacyAuthorization) {
			wx.onNeedPrivacyAuthorization(resolve => {
				// 用户触发隐私同意时，跳转至隐私同意页面
				wx.navigateTo({
					url: "/pages/privacy/index",
				});
				// resolve事件绑定至全局的prototype
				Vue.prototype.$resolvePrivacyAuthorization = resolve;
			});
		}

		getVersion({sourceType: 1}).then(res => {
			if (res.result == "1" && res.data) {
				this.$store.commit("updateAppNewVersionObj", res.data);
			}
		})
	},
	onShow: function () {
		console.log("App Show");
	},
	onHide: function () {
		console.log("App Hide");
	},
};
</script>
<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import "uview-ui/index.scss";
</style>
<style>
@import "@/assets/css/main.css";
@import "/wxcomponents/vant/common/index.wxss";
</style>
