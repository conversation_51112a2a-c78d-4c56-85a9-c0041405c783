/*每个页面公共css */
page {
	background: #F2F4F7;
	width: 100vw;
	height: 100vh;
	color: #272727;
}

.flex-row {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.flex-col {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.flex-row-start {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.center-col {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.space-between-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.divid-line {
	width: 100%;
	height: 1rpx;
	background: #E8E8E8;
}

/* 右翻转 */
.flip-vertical-right {
	-webkit-animation: flip-vertical-right 0.4s cubic-bezier(0.455, 0.030, 0.515, 0.955) alternate-reverse;
	animation: flip-vertical-right 0.4s cubic-bezier(0.455, 0.030, 0.515, 0.955) alternate-reverse;
}

@-webkit-keyframes flip-vertical-right {
	0% {
		-webkit-transform: rotateY(0);
		transform: rotateY(0);
	}

	100% {
		-webkit-transform: rotateY(180deg);
		transform: rotateY(180deg);
	}
}

@keyframes flip-vertical-right {
	0% {
		-webkit-transform: rotateY(0);
		transform: rotateY(0);
	}

	100% {
		-webkit-transform: rotateY(180deg);
		transform: rotateY(180deg);
	}
}

::v-deep .u-popup__content {
	border-radius: 30rpx;
}

::v-deep .u-toolbar {
	border-bottom: 1rpx solid #edeef2;
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 36rpx;
}