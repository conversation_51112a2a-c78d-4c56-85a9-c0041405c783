/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-16 23:53:54
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-03-11 22:48:53
 * @FilePath: /funds_approve_mobile/src/store/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const store = new Vuex.Store({
    state: {
        scoreList: [],
        openId: '',
        appNewVersionObj: {},
    },
    mutations: {
        updateScoreList(state, data) {
            state.scoreList = data;
        },
        updateOpenId(state, data) {
            state.openId = data;
        },
        updateAppNewVersionObj(state, data) {
            state.appNewVersionObj = data || {};
        }
    }
})

export default store;
