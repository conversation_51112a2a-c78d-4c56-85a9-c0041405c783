<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="我的课程"
				title=" "
				:autoBack="false"
				leftIconSize="0"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>
			<view class="top-foot">
				<u-swiper
					:list="list"
					@change="e => (current = e.current)"
					@click="previewImage"
					keyName="advertPic"
					height="281.94rpx"
					radius="20rpx"></u-swiper>
			</view>
		</view>
		<template v-if="hiddenCourse">
			<view
				class="main"
				:style="'top:' + topHeight + 'px;'">
				<view
					style="width: 100%">
					<u-empty
						mode="list"
						text="暂无数据"
						:icon="require('../../static/icon/order-empty.png')"></u-empty>
				</view>
			</view>
		</template>
		<template v-else>
			<view
				class="main"
				:style="'top:' + topHeight + 'px;'">
				<view
					style="width: 100%"
					v-if="studyPackageList.length == 0">
					<u-empty
						mode="list"
						text="暂无课程"
						:icon="require('../../static/icon/order-empty.png')"></u-empty>
				</view>
				<scroll-view
					scroll-y="true"
					class="scroll-L"
					:style="'height:' + 'calc(100vh - ' + topHeight + 'px)'"
					v-if="studyPackageList.length > 0">
					<view
						class="leftMenu"
						v-for="(item, index) in studyPackageList"
						:key="item.id">
						<!-- 一级菜单 -->
						<view
							:class="selectLeftItem.courseId == item.courseId ? 'leftMenu-title active' : 'leftMenu-title'"
							@click="selectLeftMenu(item, index)">
							<image
								:src="imagebaseurl + 'static/icon/courses_icon_' + (index + 1) + '.png'"
								mode=""></image>
							<text>{{ item.courseName }}</text>
						</view>
					</view>
				</scroll-view>
				<scroll-view
					scroll-y="true"
					class="scroll-R"
					:style="'height:' + 'calc(100vh - ' + (topHeight + 10) + 'px)'"
					v-if="studyPackageList.length > 0">
					<view
						class="rightMenu"
						v-for="(item, index) in studyPackageProfessionList">
						<view
							:class="selectRighttItem.professionId == item.professionId ? 'rightMenu-title-active' : 'rightMenu-title'"
							@click="openRightMenu(item)">
							<view class="rightMenu-title-right">
								<image
									:src="imagebaseurl + 'static/icon/courses_icon_4.png'"
									mode=""></image>
								<text>{{ item.professionName }}</text>
							</view>
							<u-icon
								:name="selectRighttItem.professionId == item.professionId ? 'arrow-up' : 'arrow-down'"
								size="26.17rpx"></u-icon>
						</view>
						<view
							class="rightMenu-box"
							:style="selectRighttItem.professionId == item.professionId ? 'height:' + chapterListHeight + 'rpx' : ''">
							<view
								:class="oldChildenRightItem.chapterId == item2.chapterId ? 'rightMenu-content active' : 'rightMenu-content'"
								v-for="(item2, index2) in chapterList"
								@click="openRightMenuChilden(item2, index2)">
								<view class="right-content-l">
									<text>{{ item2.chapterName }}</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</template>
		
		<u-picker
			:show="show"
			:columns="columns"
			confirmColor="#2168fe"
			@cancel="show = false"
			@confirm="show = false"></u-picker>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import {
	getAdList,
	getStudyPackageCourseList,
	getStudyPackageProfessionList,
	getStudyPackageChapterList,
	getStudyPackagePage,
	getSwitchConfig,
	getUserInfo,
} from "../../api/api.js";
export default {
	data() {
		return {
			current: 0,
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			leftMenu: [
				{
					id: 1,
					name: "初试系统课程",
					image: "",
				},
				{
					id: 2,
					name: "初试冲刺课程",
					image: "static/icon/courses_icon_2.png",
				},
				{
					id: 3,
					name: "复试讲义",
					image: "static/icon/courses_icon_3.png",
				},
			],
			// 一级菜单
			selectLeftItem: {},
			// 一级菜单
			selectRighttItem: {},
			// 二级菜单
			oldChildenRightItem: {},
			//图片基准地址
			imagebaseurl: imagebaseurl,
			show: false,
			columns: [["陕西省", "西北政法大学"]],
			list: [],
			user: uni.getStorageSync("user"),
			//学习包课程列表
			studyPackageList: [],
			//获取学习包专业列表
			studyPackageProfessionList: [],
			//获取学习包章节列表
			chapterList: [],
			chapterListHeight: 0,
			params: {
				pageNo: 1,
				pageSize: 10,
			},
			userConfig: {},
			hiddenCourse: false,

			newVersionObj: {},
		};
	},
	created() {
		// 判断当前版本是否处于审核中，如果是审核中则不让点击
		this.newVersionObj = this.$store.state.appNewVersionObj;
		if (this.newVersionObj.versionNo === this.$VERSION && this.newVersionObj.publishStatus == 2) {
			this.hiddenCourse = true;
		}
		this.getUser();
	},
	onShow() {
		getSwitchConfig({ orderType: 6 })
		.then(res => {
			console.log("用户订单状态：", res);
			if (res.result == "1") {
				this.userConfig = res.data;
			} else {
				this.$showToast(res.message || "配置查询异常，请重试");
			}
		})
		.catch(err => {
			this.$showToast(err.message || "配置查询异常，请重试");
		});
	},
	mounted() {
		this.getElementHeight();
		this.getAdListInfo();
		this.getStudyPackage();
	},
	methods: {
		previewImage() {
			let currAd = this.list[this.current];
			if (currAd.jumpParam) {
				if (currAd.jumpType == 1) {
				uni.navigateTo({
					url: currAd.jumpParam,
				});

				return;
				}else if (currAd.jumpType == 3) {
				wx.navigateToMiniProgram({
					appId: currAd.jumpParam,
					fail(err) {
					// 打开失败
					console.log(">>打开商城失败>>err>>", err);
					uni.showModal({
						title: "提示",
						content: "跳转失败，请在微信小程序中搜索该小程序",
						showCancel: false,
						success: function (res) {
						}
					});
					},
					success(res) {
					// 打开成功
					}
				})
				
				return;
				}
			}

			let urls = [];
			this.list.map(v => {
				urls.push(v.advertPic);
			});
			uni.previewImage({
				current: this.current,
				urls: [...urls], // 需要预览的图片http链接列表
			});
		},
		getUser() {
			getUserInfo({
				masterType: this.user.masterType,
				globalSchoolId: this.user.globalSchoolId,
				openId: this.openid,
			}).then(res => {
				if (res.result == "1") {
					this.user = res.data;
					uni.removeStorageSync("user");
					uni.setStorageSync("user", res.data);
					// 只要有审核中的课程，我的账号所有历史版本也都隐藏课程
					let phone = res.data.phone || "";
					if (this.newVersionObj.publishStatus == 2 && phone == "18916612959") {
						this.hiddenCourse = true;
					}
				} else {
					this.$showToast(res.message || "获取用户信息失败");
				}
			})
			.catch(err => {
				this.$showToast(err.message || "获取用户信息失败");
			});
		},
		selectLeftMenu(item, index) {
			this.selectLeftItem = this.studyPackageList[index];
			this.getStudyPackageProfessio();
		},
		//获取学习包专业列表
		getStudyPackageProfessio() {
			if (Object.keys(this.selectLeftItem).length > 0) {
				this.params.courseId = this.selectLeftItem.courseId;
				getStudyPackageProfessionList(this.params)
					.then(res => {
						if (res.result == "1") {
							this.studyPackageProfessionList = res.data.list;
						} else {
							this.$showToast(res.message || "查询异常，请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "查询异常，请重试");
					});
			}
		},
		//获取学习包课程列表
		getStudyPackage() {
			this.params.masterType = this.user.masterType;
			this.params.globalSchoolId = this.user.globalSchoolId;
			// this.params.globalProfessionId = this.user.globalProfessionId;
			getStudyPackageCourseList(this.params)
				.then(res => {
					if (res.result == "1") {
						this.studyPackageList = res.data.list;
						if (this.studyPackageList.length > 0) {
							this.selectLeftItem = this.studyPackageList[0];
							this.getStudyPackageProfessio();
						}
					} else {
						this.$showToast(res.message || "查询异常，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询异常，请重试");
				});
		},
		//课程列表轮播图
		getAdListInfo() {
			getAdList({
				masterType: this.user.masterType,
				advertType: 4,
			})
				.then(res => {
					if (res.result == "1") {
						this.list = res.data.list;
					} else {
						this.$showToast(res.message || "查询异常，请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "查询异常，请重试");
				});
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},

		openRightMenuChilden(item2, index2) {
			this.oldChildenRightItem = item2;
			let courseType;
			if (this.selectLeftItem.courseName.indexOf("系统课程") > -1) {
				courseType = 1;
			} else if (this.selectLeftItem.courseName.indexOf("冲刺课程") > -1) {
				courseType = 2;
			} else if (this.selectLeftItem.courseName.indexOf("复试课程") > -1) {
				courseType = 3;
			} else if (this.selectLeftItem.courseName.indexOf("法律综合") > -1) {
				courseType = 4;
			} else if (this.selectLeftItem.courseName.indexOf("法律基础") > -1) {
				courseType = 5;
			}
			let typeObj = this.userConfig[0].studyPackageList.find(item => item.orderExtCourseType == courseType);
			if (typeObj && typeObj.typeSwitch == 1) {
				uni.navigateTo({
					url: "/pages/userPage/myCoursesPlay?repositoryId=" + item2.repositoryId,
				});
			} else {
				if (index2 <= 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买课程",
						cancelColor: "#c6c6c6",
						cancelText: "继续预览",
						// confirmColor: "#0693ff",
						confirmText: "购买课程",
						success: res => {
							if (res.confirm) {
								uni.setStorageSync("startPage", uni.$u.page());
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=" + courseType,
								});
							} else if (res.cancel) {
								uni.navigateTo({
									url: "/pages/userPage/myCoursesPlay?repositoryId=" + item2.repositoryId,
								});
							}
						},
					});
				}
				if (index2 > 3) {
					uni.showModal({
						title: "提示",
						content: "您还未购买课程",
						cancelColor: "#c6c6c6",
						cancelText: "取消",
						// confirmColor: "#0693ff",
						confirmText: "购课程",
						success: res => {
							if (res.confirm) {
								uni.setStorageSync("startPage", uni.$u.page());
								uni.navigateTo({
									url: "/pages/order/orderPay?courseType=" + courseType,
								});
							}
						},
					});
				}
			}
		},
		openRightMenu(item) {
			if (this.selectRighttItem.professionId == item.professionId) {
				this.selectRighttItem = {};
			} else {
				this.selectRighttItem = item;

				this.params.professionId = this.selectRighttItem.professionId;
				getStudyPackageChapterList(this.params)
					.then(res => {
						if (res.result == "1") {
							this.chapterList = res.data.list;
							this.chapterListHeight = res.data.list.length * 100;
						} else {
							this.$showToast(res.message || "查询异常，请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "查询异常，请重试");
					});
			}
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f6f7fb 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	// background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;

	.top-foot {
		margin-top: 20rpx;
		width: calc(100% - 80rpx);
		height: 281.94rpx;
		border-radius: 20rpx;
	}
}
.main {
	margin-top: 30rpx;
	width: 100%;
	position: fixed;
	display: flex;
	justify-content: flex-start;
	z-index: 9;
}
.scroll-L {
	width: 280rpx;
	background: #ffffff;
	border-radius: 0rpx 30rpx 0rpx 0rpx;
}
.scroll-R {
	width: calc(100% - 280rpx);
}
.rightMenu {
	width: calc(100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	.rightMenu-title {
		width: calc(100% - 60rpx);

		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;

			image {
				width: 28.82rpx;
				height: 28.82rpx;
			}
			text {
				width: 317rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #8590a7;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.rightMenu-title-active {
		width: calc(100% - 60rpx);

		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.rightMenu-title-right {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			gap: 18rpx;

			image {
				width: 28.82rpx;
				height: 28.82rpx;
			}
			text {
				width: 317rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1f2638;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.rightMenu-box {
		width: calc(100% - 47rpx);
		height: 0;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		transition: height 0.5s ease-in-out;
		.rightMenu-content {
			margin-top: 18rpx;
			margin-bottom: 18rpx;
			margin-left: 12rpx;
			width: calc(100% - 21rpx);
			height: 79rpx;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
			display: flex;
			align-items: center;

			.right-content-l {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				gap: 20rpx;
				font-weight: 500;
				font-size: 28rpx;

				text {
					margin-left: 25rpx;
				}
				.sing {
					padding: 5rpx;
					width: 52rpx;
					height: 28rpx;
					background: #dce7ff;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					text {
						margin-left: 0;
						font-weight: 400;
						font-size: 20rpx;
						color: #2168fe;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
		}

		.active {
			width: calc(100% - 21rpx);
			height: 79rpx;
			background: #2168fe;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
			color: white !important;
			.sing {
				padding: 5rpx;
				width: 52rpx;
				height: 28rpx;
				background: white !important;
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				text {
					margin-left: 0;
					font-weight: 400;
					font-size: 20rpx;
					color: #2168fe;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}
}
.leftMenu {
	width: 280rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.leftMenu-title {
		margin-top: 20rpx;
		width: 244rpx;
		height: 80rpx;
		background: #f6f7fb;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 10rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #8590a7;
		image {
			margin-left: 16rpx;
			width: 32rpx;
			height: 32rpx;
		}
	}
	.active {
		background: rgba(38, 153, 240, 0.1);
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #2168fe;
	}
}
</style>
