<template>
	<view class="container">
		<view class="top">
			<u-navbar
				leftText="我的课程"
				title=" "
				:autoBack="true"
				leftIconSize="25"
				:bgColor="bgColor"
				safeAreaInsetTop
				placeholder></u-navbar>

			<view class="top-video">
				<!-- :src="payStudyInfo.studyPackageFile" -->
				<video
					id="myVideo"
					:src="payStudyInfo.studyPackageFile"
					:initial-time="payStudyInfo.userVideoTime"
					@error="videoErrorCallback"
					@timeupdate="timeupdate"
					enable-play-gesture
					autoplay
					show-loading></video>
			</view>
		</view>

		<view
			class="main"
			:style="'top:' + topHeight + 'px;'">
			<view class="card">
				<view class="tabs">
					<view
						class="tabs-box"
						v-for="(item, index) in tabs"
						:key="index"
						@click="selectTab(item)">
						<text :class="selectedTab.id == item.id ? 'tabs-title active' : 'tabs-title'">{{ item.name }}</text>
						<view :class="selectedTab.id == item.id ? 'line-active' : 'line'"></view>
					</view>
				</view>
				<scroll-view
					scroll-y="true"
					class="scroll-Y">
					<view class="scroll-view">
						<view
							class="studyItem"
							:style="payStudyInfo.repositoryDetailId == item.repositoryDetailId ? '' : 'color:#afa9a9'"
							v-for="(item, index) in studyInfo"
							:key="index"
							@click="selectStudyInfo(item)">
							<view class="study-left">
								<u-icon
									:name="payStudyInfo.repositoryDetailId == item.repositoryDetailId ? 'play-circle-fill' : 'play-circle'"
									:color="payStudyInfo.repositoryDetailId == item.repositoryDetailId ? '#1e83d9' : ''"></u-icon>
								<text> {{ item.studyPackageName }}</text>
							</view>

							<view class="right">
								<text>已学： {{ item.studyPercent }}%</text>
								<text>观看人数：{{ item.watchUserCount }}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import { imagebaseurl } from "../../api/index.js";
import { getStudyPackagePage, reportUserStudyPackage } from "../../api/api.js";
export default {
	data() {
		return {
			bgColor: "rgba(255,255,255,0)",
			topHeight: 0,
			tabs: [
				{
					id: 1,
					name: "课程列表",
				},
			],
			selectedTab: {},
			repositoryId: 0,
			studyInfo: [],
			payStudyInfo: {},
			currentTime: 0,
			params: {
				pageNo: 1,
				pageSize: 10,
			},
		};
	},
	onLoad(option) {
		this.repositoryId = option.repositoryId;
		this.selectedTab = this.tabs[0];
		this.getStudyInfo();
	},
	mounted() {
		this.getElementHeight();
	},
	methods: {
		selectStudyInfo(item) {
			this.reportUserStudy(item);
		},
		reportUserStudy(item) {
			const currentTime = this.currentTime - this.payStudyInfo.userVideoTime;
			if (this.currentTime > 0) {
				reportUserStudyPackage({
					studyPackageRepositoryId: this.payStudyInfo.studyPackageRepositoryId,
					studyPackageRepositoryDetailId: this.payStudyInfo.repositoryDetailId,
					videoTime: currentTime,
					userPlayTime: this.currentTime,
				})
					.then(res => {
						if (res.result == "1") {
							this.currentTime = 0;
							this.payStudyInfo = item;
						} else {
							this.$showToast(res.message || "上报异常请重试");
						}
					})
					.catch(err => {
						this.$showToast(err.message || "上报异常请重试");
					});
			}
		},
		timeupdate(event) {
			this.currentTime = event.detail.currentTime;
		},
		getStudyInfo() {
			this.params.studyPackageRepositoryId = this.repositoryId;
			getStudyPackagePage(this.params)
				.then(res => {
					if (res.result == "1") {
						this.studyInfo = res.data.list;
						this.payStudyInfo = this.studyInfo[0];
					} else {
						this.$showToast(res.message || "获取课程异常请重试");
					}
				})
				.catch(err => {
					this.$showToast(err.message || "获取课程异常请重试");
				});
		},
		videoErrorCallback(e) {
			this.$showToast(e.target.errMsg || "视频资源加载失败");
		},
		getElementHeight() {
			let that = this;
			// 创建选择器查询
			const query = uni.createSelectorQuery().in(this);
			// 选择我们想要的元素
			query
				.select(".top")
				.boundingClientRect(data => {
					// data是一个包含元素尺寸信息的对象，如果元素存在，其高度可以通过data.height获取
					if (data) {
						console.log("元素的高度：", data.height);
						that.topHeight = data.height;
					}
				})
				.exec(); // 执行查询
		},
	},
};
</script>

<style lang="scss">
::v-deep .u-navbar__content__left__text {
	font-weight: bold !important;
	font-size: 36rpx !important;
	color: #1f2638 !important;
}

.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(180deg, #bbddfa 0%, #f1f2f5 33%);
}
.top {
	width: 100%;
	// min-height: 272rpx;
	// background: linear-gradient(180deg, #bbddfa 24%, #f6f7fb 317%);
	position: fixed;
	top: 0;
	z-index: 99;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.top-video {
	width: 100%;
}
#myVideo {
	width: 100%;
}
.main {
	margin-top: 10rpx;
	width: 100%;
	z-index: 9;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.card {
	width: calc(100% - 60rpx);
	height: 960rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.tabs {
	padding-left: 39rpx;
	width: calc(100% - 39rpx);
	background-color: white;
	display: flex;
	justify-content: flex-start;
	gap: 88rpx;
	border-bottom: 1rpx solid #ebecf0;
	.tabs-box {
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 22rpx;
		.tabs-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #646d80;
		}
		.active {
			color: #2168fe;
		}
		.line {
			width: 94rpx;
			height: 4rpx;
			background: white;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
		.line-active {
			width: 94rpx;
			height: 4rpx;
			background: #2168fe;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
	}
}
.studyItem {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
	width: calc(100% - 65rpx);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #ebecf0;
	.study-left {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 20rpx;
		text {
			font-size: 25rpx;
		}
	}
	.right {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 20rpx;
		text {
			font-size: 25rpx;
		}
	}
}
.scroll-Y {
	height: 900rpx;
	.scroll-view {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
}
</style>
