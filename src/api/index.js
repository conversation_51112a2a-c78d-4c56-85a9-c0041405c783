/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-16 23:56:05
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-18 22:43:32
 * @FilePath: /funds_approve_mobile/src/api/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// const baseURL = "http://*************:39527";
// const baseURL = "http://localhost:39527";
const baseURL = 'https://api.ligeedu.cn/';

/**
 *
 * @param {*} url
 * @param {*} parma
 * @param {*} bloading
 * @returns
 */
export const postResponse = (url, parma, bloading = true) => {
	return new Promise((resolve, reject) => {
		if (bloading) {
			uni.showLoading({
				title: '加载中',
			});
		}
		const token = uni.getStorageSync('token');
		const userInfo = uni.getStorageSync('user') || {};
		if (Object.keys(userInfo).length > 0) {
			if (uni.$u.test.object(parma)) {
				parma.globalSchoolId = userInfo.globalSchoolId
			}
		}
		if (!uni.getStorageSync('openId')) {
			console.warn(`openid获取本地存储失败${baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
		}
		console.log(`${baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma));
		if (parma.masterType == null || parma.masterType == undefined || parma.masterType == "") {
			parma.masterType = 1;
			parma.globalSchoolId = '-68';
		}

		uni.request({
			url: `${baseURL}${url}`,
			data: parma,
			header: {
				'Content-Type': 'application/json;charset=UTF-8',
				'token': token,
				'openid': uni.getStorageSync('openId'),
			},
			method: 'POST',
			success: (res) => {
				// console.log(`${baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data));
				if (res.data.result == "10000") {
					// uni.showToast({
					// 	title: "登录已失效，即将前往登录",
					// 	icon: "none",
					// 	success() {
					// 		setTimeout(() => {
					// 			uni.reLaunch({
					// 				url: "/pages/login/phoneLogin",
					// 			});
					// 		}, 500);
					// 	},
					// });
					uni.showModal({
						title: "提示",
						content: "您还未登录，即将前往登录",
						showCancel: true,
						cancelText: "暂不登录",
						success: function (res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/phoneLogin",
								});
							}
						},
					});
					return;
				}
				resolve(res.data);
			},
			fail: (err) => {
				console.error(`${baseURL}${url}接口错误：>>>>>>>>`, err);
				reject(err);
			},
			complete: () => {
				uni.hideLoading();
			},
		});
	});
};

/**
 *
 * @param {*} url
 * @param {*} parma
 * @param {*} bloading
 * @returns
 */
export const getResponse = (url, parma, bloading = true) => {
	return new Promise((resolve, reject) => {
		if (bloading) {
			uni.showLoading({
				title: '加载中',
			});
		}
		const token = uni.getStorageSync('token');
		console.log(`${baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma));
		const userInfo = uni.getStorageSync('user') || {};

		if (Object.keys(userInfo).length > 0) {
			if (uni.$u.test.object(parma)) {
				parma.globalSchoolId = userInfo.globalSchoolId
			}
		}
		if (!uni.getStorageSync('openId')) {
			console.warn(`openid获取本地存储失败${baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
		}
		console.log(`${baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma));
		if (parma.masterType == null || parma.masterType == undefined || parma.masterType == "") {
			parma.masterType = 1;
			parma.globalSchoolId = '-68';
		}

		uni.request({
			url: `${baseURL}${url}`,
			data: parma,
			header: {
				'Content-Type': 'application/json;charset=UTF-8',
				'token': token,
				'openid': uni.getStorageSync('openId'),
			},
			method: 'GET',
			success: (res) => {
				// console.log(`${baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data));
				console.log(res);
				if (res.data.result == "10000") {
					// uni.showToast({
					// 	title: "登录已失效，即将前往登录",
					// 	icon: "none",
					// 	success() {
					// 		setTimeout(() => {
					// 			uni.reLaunch({
					// 				url: "/pages/login/phoneLogin",
					// 			});
					// 		}, 500);
					// 	},
					// });
					uni.showModal({
						title: "提示",
						content: "您还未登录，即将前往登录",
						showCancel: true,
						cancelText: "暂不登录",
						success: function (res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/phoneLogin",
								});
							}
						},
					});
					return;
				}
				resolve(res.data);
			},
			fail: (err) => {
				console.error(`${baseURL}${url}接口错误：>>>>>>>>`, err);
				reject(err);
			},
			complete: () => {
				uni.hideLoading();
			},
		});
	});
};

export const uploadResponse = (url, filePath, fileName, formData, bloading = true) => {
	return new Promise((resolve, reject) => {
		if (bloading) {
			uni.showLoading();
		}

		uni.uploadFile({
			url: `${baseURL}${url}`,
			filePath: filePath,
			name: fileName,
			formData: formData,
			success: (res) => {
				// console.log(`${baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data));
				resolve(res.data);
			},
			fail: (err) => {
				console.log(`${baseURL}${url}接口错误：>>>>>>>>`, err);
				reject(err);
			},
			complete: () => {
				uni.hideLoading();
			},
		});
	});
};
//图片基本地址，回头替换
export const imagebaseurl = '../../'

export default {
	postResponse,
	getResponse,
	uploadResponse,
	imagebaseurl
};