/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-16 23:57:40
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-02-10 22:21:27
 * @FilePath: /funds_approve_mobile/src/api/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from './index';

/**
 * 通过code 获取微信openid
 */
// api列表
// 登录
export const requestOpenId = (params) => http.getResponse('/wx/getWxUserInfoByCode', params, false);
// 获取版本号
export const getVersion = (params) => http.getResponse('/app/home/<USER>', params, false);
// 查询手机号
export const requestEncryptedData = (params) =>
	http.postResponse('/wx/encryptedData', params);
// 上传图片
export const uploadFile = (params, bloading = true) =>
	http.uploadResponse('/uploadFile', params.filePath, params.fileName, params.formData, bloading);
//更新用户信息
export const updateUser = (params) => http.postResponse('/app/user/updateUserInfo', params, true);
//七牛获取上传参数
export const getUploadConfigUrl = (params) => http.getResponse('/getUploadConfig', params, true);
//获取文件url
export const getFileUrl = (params) => http.postResponse('/getFileUrl', params, true);
//首页接口
export const getHome = (params) => http.getResponse('/applets/getHome', params, false);
//获取公告列表
export const getAnnouncementList = (params) =>
	http.getResponse('/applets/getAnnouncementList', params, false);
//获取广告列表
export const getAdList = (params) => http.getResponse('/applets/getAdvertList', params, false);
//检验用户昵称是否重复
export const checkNickName = (params) => http.getResponse('/applets/checkNickName', params);
//获取开课的学校
export const getSchoolList = (params) => http.getResponse('/applets/getSchoolList', params, false);
//获取专业列表
export const getProfessionList = (params) =>
	http.getResponse('/applets/getSchoolProfessionList', params, false);
//获取分类学校筛选树
export const getSchoolTree = (params) => http.getResponse('/applets/getSchoolTree', params, false);
//考研剩余多少天
export const getExamTime = (params) => http.getResponse('/app/home/<USER>', params, false);
//用户注册
export const register = (params) => http.postResponse('/applets/register', params);
//弃用
export const goLogin = (params) => http.postResponse('/applets/login', params, true);

/* 小程序真题库部分接口 */
//获取学校 -真题题库
export const realSubjectRepositoryGetSchoolList = (params) =>
	http.getResponse('/applets/realSubjectRepository/getSchoolList', params, false);
//查询学校下的课程- -真题题库
export const realSubjectRepositoryGetCourseList = (params) =>
	http.getResponse('/applets/realSubjectRepository/getCourseList', params, false);
//查询课程下的专业 - 真题题库
export const realSubjectRepositoryGetProfessionList = (params) =>
	http.getResponse('/applets/realSubjectRepository/getProfessionList', params, false);
//查询自定义层级名称 - 真题题库
export const realSubjectRepositoryGetCustomizeList = (params) =>
	http.getResponse('/applets/realSubjectRepository/getCustomizeList', params, false);
//获取用户题目
export const realSubjectRepositoryGetSubjectRepositoryPage = (params) =>
	http.getResponse('/applets/realSubjectRepository/getSubjectRepositoryPage', params, false);
//上报用户真题库答案
export const reportUserSubjectAnswer = (params) =>
	http.postResponse('/applets/realSubjectRepository/reportUserSubjectAnswer', params, true);


/* 小程序讲义部分接口 */
export const realSubjectRepositorySaveAnswerReport = (params) =>
	http.postResponse('/applets/realSubjectRepository/reportUserSubjectAnswer', params, false);
//获取讲义课程列表
export const getLectureCourseList = (params) =>
	http.getResponse('/app/lectureNotes/getCourseList', params, false);
//获取讲义专业列表
export const getlectureNotesProfessionList = (params) =>
	http.getResponse('/app/lectureNotes/getProfessionList', params, false);
//获取讲义章节列表
export const getChapterList = (params) =>
	http.getResponse('/app/lectureNotes/getChapterList', params, false);
//获取讲义分页数据
export const getLectureNotesRepositoryPaget = (params) =>
	http.getResponse('/app/lectureNotes/getLectureNotesRepositoryPage', params, false);
// 上报用户讲义熟练程度
export const reportUserLectureNotes = (params) =>
	http.postResponse('/app/lectureNotes/reportUserLectureNotes', params, true);

//添加收藏夹
export const addCollection = (params) =>
	http.postResponse('/app/collect/addUserCollect', params, false);
//删除收藏夹
// export const deleteCollection = (params) => http.postResponse('/app/collect/delUserCollect', params, false)
//取消收藏夹
export const cancleCollection = (params) =>
	http.postResponse('/app/collect/delUserCollect', params, true);

/* 小程序成绩序部分接口 */
// 获取用户历年成绩
export const queryUserScore = (params) => http.getResponse('/getUserScore', params, true);
//获取学校分类列表
export const getSchoolCategoryList = (params) =>
	http.getResponse('/getSchoolClassificationList', params, false);
//获取学校列表
export const querySchoolList = (params) => http.getResponse('/getSchoolList', params, true);
//获取院系列表
export const querySchoolDepartmentList = (params) =>
	http.getResponse('/getSchoolDepartmentList', params, true);
//获取专业列表
export const querySchoolProfessionList = (params) =>
	http.getResponse('/getSchoolProfessionList', params, true);
//获取课程信息
export const querySchoolCourseList = (params) =>
	http.getResponse('/getSchoolCourseList', params, true);
// 保存成绩
export const saveUserScore = (params) => http.postResponse('/saveUserScore', params, true);
//获取banner广告
export const queryBannerList = (params) => http.getResponse('/getBanner', params, true);
//获取活动
export const getActivity = (params) => http.getResponse('/getActivity', params, false);

//用户统计
export const getUserStatistics = (params) =>
	http.getResponse('/app/user/getUserInfo', params, false);
//获取学习包课程列表
export const getStudyPackageCourseList = (params) =>
	http.getResponse('/app/studyPackage/getCourseList', params, false);
//获取学习包专业列表
export const getStudyPackageProfessionList = (params) =>
	http.getResponse('/app/studyPackage/getProfessionList', params, false);
//获取学习包章节列表
export const getStudyPackageChapterList = (params) =>
	http.getResponse('/app/studyPackage/getChapterList', params, false);
//获取学习包分页数据
export const getStudyPackagePage = (params) =>
	http.getResponse('/app/studyPackage/getStudyPackagePage', params, false);
//上报用户学习包
export const reportUserStudyPackage = (params) =>
	http.postResponse('/app/studyPackage/reportUserStudyPackage', params, true);
//用户讲义统计
export const getUserLectureNotesStatistical = (params) =>
	http.getResponse('/app/lectureNotes/getLectureNotesStatistical', params, false);
//获取掌握情况的讲义专业列表
export const getHoldProfessionListe = (params) =>
	http.getResponse('/app/lectureNotes/getHoldProfessionList', params, false);
//获取掌握情况的讲义章节列表
export const getHoldChapterList = (params) =>
	http.getResponse('/app/lectureNotes/getHoldChapterList', params, false);
//获取收藏的讲义专业列表
export const getCollectProfessionList = (params) =>
	http.getResponse('/app/lectureNotes/getCollectProfessionList', params, false);
//获取收藏的讲义章节列表
export const getCollectChapterList = (params) =>
	http.getResponse('/app/lectureNotes/getCollectChapterList', params, false);
//清空收藏夹
export const clearCollection = (params) =>
	http.postResponse('/app/lectureNotes/collect/cleanUserCollect', params, true);
//获取微信客服配置
export const getWechatConfig = (params) =>
	http.getResponse('/applets/wechat/config/getWechatConfig', params, false);
//获取开关配置
export const getSwitchConfig = (params) => http.getResponse('/app/user/getConfig', params, false);
//订单列表
export const getOrderList = (params) => http.getResponse('/app/order/orderList', params, false);
//取消订单
export const cancelOrder = (params) => http.postResponse('/app/order/cancelOrder', params, false);
//获取订单配置
export const getOrderConfig = (params) =>
	http.getResponse('/applets/wechat/config/getStudyPackageConfig', params, false);
//成绩排名
export const getScoreRanking = (params) =>
	http.getResponse('/applets/apply/wish/scoreList', params, false);
//举报成绩
export const reportScore = (params) =>
	http.postResponse('/applets/apply/wish/reportScore', params, true);
//保存用户志愿
export const saveUserWish = (params) =>
	http.postResponse('/applets/apply/wish/addApplyWish', params, true);
//查询用户填报的志愿
export const getUserWish = (params) =>
	http.getResponse('/applets/apply/wish/queryUserApplyWish', params, true);
//志愿列表
export const getWishList = (params) =>
	http.getResponse('/applets/apply/wish/applyWishList', params, false);
//继续支付
export const continuePay = (params) => http.postResponse('/app/order/continuePay', params, true);
//发送验证码接口
export const sendCode = (params) =>
	http.getResponse('/app/user/sendVerificationCode', params, true);
//登录接口
export const phoneLogin = (params) => http.postResponse('/app/user/login', params, true);
//查询openid 对应的用户数据
export const queryUserList = (params) => http.postResponse('/applets/userList', params, true);
//提交反馈
export const submitFeedback = (params) =>
	http.postResponse('/applets/submitFeedBack', params, true);
//上报有赞数据信息
export const reportYouzanData = (params) => http.postResponse('/app/order/reportOrderInfo', params, true)
//创建支付订单
export const createOrder = (params) => http.postResponse('/app/camp/createOrder', params, true);
//获取用户信息
export const getUserInfo = (params) => http.getResponse('/app/user/getUserInfo', params, false);